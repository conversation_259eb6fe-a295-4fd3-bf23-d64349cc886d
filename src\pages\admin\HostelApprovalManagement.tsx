import React, { useState } from 'react';
import { 
  Building2, 
  Eye, 
  Check, 
  X, 
  Clock, 
  MapPin, 
  User, 
  Phone, 
  Mail,
  CreditCard,
  FileText,
  Search,
  Filter
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import {
  HostelRegistration,
  mockHostelRegistrations,
  getUserById
} from '@/data/mockData';
import { NotificationService, StatusTracker } from '@/utils/notificationService';

export const HostelApprovalManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all');
  const [selectedRegistration, setSelectedRegistration] = useState<HostelRegistration | null>(null);
  const [isReviewDialogOpen, setIsReviewDialogOpen] = useState(false);
  const [reviewComments, setReviewComments] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  // Filter registrations based on search and status
  const filteredRegistrations = mockHostelRegistrations.filter(registration => {
    const matchesSearch = registration.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         registration.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         registration.ownerName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || registration.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleApprove = async (registration: HostelRegistration) => {
    setIsProcessing(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update registration status
      const oldStatus = registration.status;
      registration.status = 'approved';
      registration.reviewedDate = new Date().toISOString().split('T')[0];
      registration.reviewedBy = '1'; // Admin user ID
      registration.reviewComments = reviewComments;

      // Track status change
      StatusTracker.trackRegistrationStatusChange(
        registration.id,
        oldStatus,
        'approved',
        '1', // Admin user ID
        reviewComments
      );

      // Notify owner of approval
      NotificationService.notifyOwnerOfApprovalStatus(
        registration.ownerId,
        registration.name,
        'approved'
      );

      toast({
        title: 'Hostel Approved',
        description: `${registration.name} has been approved successfully.`,
      });

      setIsReviewDialogOpen(false);
      setReviewComments('');
      setSelectedRegistration(null);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to approve hostel. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleReject = async (registration: HostelRegistration) => {
    if (!rejectionReason.trim()) {
      toast({
        title: 'Rejection Reason Required',
        description: 'Please provide a reason for rejection.',
        variant: 'destructive',
      });
      return;
    }

    setIsProcessing(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update registration status
      const oldStatus = registration.status;
      registration.status = 'rejected';
      registration.reviewedDate = new Date().toISOString().split('T')[0];
      registration.reviewedBy = '1'; // Admin user ID
      registration.rejectionReason = rejectionReason;
      registration.reviewComments = reviewComments;

      // Track status change
      StatusTracker.trackRegistrationStatusChange(
        registration.id,
        oldStatus,
        'rejected',
        '1', // Admin user ID
        rejectionReason
      );

      // Notify owner of rejection
      NotificationService.notifyOwnerOfApprovalStatus(
        registration.ownerId,
        registration.name,
        'rejected',
        rejectionReason
      );

      toast({
        title: 'Hostel Rejected',
        description: `${registration.name} has been rejected.`,
      });

      setIsReviewDialogOpen(false);
      setReviewComments('');
      setRejectionReason('');
      setSelectedRegistration(null);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to reject hostel. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const getStatusIcon = (status: HostelRegistration['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'approved':
        return <Check className="h-4 w-4" />;
      case 'rejected':
        return <X className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: HostelRegistration['status']) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
    }
  };

  const pendingCount = mockHostelRegistrations.filter(r => r.status === 'pending').length;
  const approvedCount = mockHostelRegistrations.filter(r => r.status === 'approved').length;
  const rejectedCount = mockHostelRegistrations.filter(r => r.status === 'rejected').length;

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Hostel Approval Management</h1>
        <p className="text-muted-foreground">
          Review and manage hostel registration requests
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Review</p>
                <p className="text-2xl font-bold text-yellow-600">{pendingCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Check className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Approved</p>
                <p className="text-2xl font-bold text-green-600">{approvedCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <X className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Rejected</p>
                <p className="text-2xl font-bold text-red-600">{rejectedCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Building2 className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Requests</p>
                <p className="text-2xl font-bold text-blue-600">{mockHostelRegistrations.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search by hostel name, city, or owner..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="w-full md:w-48">
              <Select value={statusFilter} onValueChange={(value: any) => setStatusFilter(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Registrations Table */}
      <Card>
        <CardHeader>
          <CardTitle>Registration Requests</CardTitle>
          <CardDescription>
            Review hostel registration submissions and take action
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Hostel Details</TableHead>
                  <TableHead>Owner</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Capacity</TableHead>
                  <TableHead>Submitted</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRegistrations.map((registration) => (
                  <TableRow key={registration.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <p className="font-medium">{registration.name}</p>
                        <p className="text-sm text-muted-foreground">
                          ₹{registration.pricePerBed.toLocaleString()}/bed
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <p className="font-medium">{registration.ownerName}</p>
                        <p className="text-sm text-muted-foreground">{registration.ownerEmail}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <p className="font-medium">{registration.city}</p>
                        <p className="text-sm text-muted-foreground">{registration.state}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <p className="font-medium">{registration.totalBeds} beds</p>
                    </TableCell>
                    <TableCell>
                      <p className="text-sm">
                        {new Date(registration.submittedDate).toLocaleDateString()}
                      </p>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant="outline" 
                        className={`flex items-center gap-1 w-fit ${getStatusColor(registration.status)}`}
                      >
                        {getStatusIcon(registration.status)}
                        {registration.status.charAt(0).toUpperCase() + registration.status.slice(1)}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => setSelectedRegistration(registration)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            Review
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                          <DialogHeader>
                            <DialogTitle>Review Hostel Registration</DialogTitle>
                            <DialogDescription>
                              Review the details and approve or reject this hostel registration
                            </DialogDescription>
                          </DialogHeader>
                          
                          {selectedRegistration && (
                            <RegistrationReviewDialog 
                              registration={selectedRegistration}
                              onApprove={() => {
                                setIsReviewDialogOpen(true);
                              }}
                              onReject={() => {
                                setIsReviewDialogOpen(true);
                              }}
                              reviewComments={reviewComments}
                              setReviewComments={setReviewComments}
                              rejectionReason={rejectionReason}
                              setRejectionReason={setRejectionReason}
                              isProcessing={isProcessing}
                              handleApprove={() => handleApprove(selectedRegistration)}
                              handleReject={() => handleReject(selectedRegistration)}
                            />
                          )}
                        </DialogContent>
                      </Dialog>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredRegistrations.length === 0 && (
            <div className="text-center py-8">
              <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No registration requests found</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

// Separate component for the registration review dialog content
interface RegistrationReviewDialogProps {
  registration: HostelRegistration;
  onApprove: () => void;
  onReject: () => void;
  reviewComments: string;
  setReviewComments: (comments: string) => void;
  rejectionReason: string;
  setRejectionReason: (reason: string) => void;
  isProcessing: boolean;
  handleApprove: () => void;
  handleReject: () => void;
}

const RegistrationReviewDialog: React.FC<RegistrationReviewDialogProps> = ({
  registration,
  reviewComments,
  setReviewComments,
  rejectionReason,
  setRejectionReason,
  isProcessing,
  handleApprove,
  handleReject,
}) => {
  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Hostel Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <Label className="text-sm font-medium">Name</Label>
              <p className="text-sm">{registration.name}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Description</Label>
              <p className="text-sm text-muted-foreground">{registration.description}</p>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">Total Beds</Label>
                <p className="text-sm">{registration.totalBeds}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Price per Bed</Label>
                <p className="text-sm">₹{registration.pricePerBed.toLocaleString()}</p>
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium">Amenities</Label>
              <div className="flex flex-wrap gap-1 mt-1">
                {registration.amenities.map((amenity) => (
                  <Badge key={amenity} variant="secondary" className="text-xs">
                    {amenity}
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Location
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <Label className="text-sm font-medium">Address</Label>
              <p className="text-sm">{registration.address}</p>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">City</Label>
                <p className="text-sm">{registration.city}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">State</Label>
                <p className="text-sm">{registration.state}</p>
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium">Pincode</Label>
              <p className="text-sm">{registration.pincode}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Contact and Legal Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <Label className="text-sm font-medium">Owner</Label>
              <p className="text-sm">{registration.ownerName}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Contact Person</Label>
              <p className="text-sm">{registration.contactPerson}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Phone</Label>
              <p className="text-sm">{registration.contactPhone}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Email</Label>
              <p className="text-sm">{registration.contactEmail}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Legal & Banking
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <Label className="text-sm font-medium">License Number</Label>
              <p className="text-sm">{registration.licenseNumber}</p>
            </div>
            {registration.gstNumber && (
              <div>
                <Label className="text-sm font-medium">GST Number</Label>
                <p className="text-sm">{registration.gstNumber}</p>
              </div>
            )}
            <div>
              <Label className="text-sm font-medium">Bank Account</Label>
              <p className="text-sm">{registration.bankAccountDetails.accountNumber}</p>
              <p className="text-xs text-muted-foreground">
                {registration.bankAccountDetails.bankName} - {registration.bankAccountDetails.ifscCode}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Review Section */}
      <Card>
        <CardHeader>
          <CardTitle>Review & Decision</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="reviewComments">Review Comments (Optional)</Label>
            <Textarea
              id="reviewComments"
              placeholder="Add any comments about this registration..."
              value={reviewComments}
              onChange={(e) => setReviewComments(e.target.value)}
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="rejectionReason">Rejection Reason (Required for rejection)</Label>
            <Textarea
              id="rejectionReason"
              placeholder="Provide detailed reason for rejection..."
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              rows={3}
            />
          </div>

          {registration.status === 'pending' && (
            <div className="flex gap-3 pt-4">
              <Button 
                onClick={handleApprove}
                disabled={isProcessing}
                className="flex-1"
              >
                <Check className="h-4 w-4 mr-2" />
                Approve Registration
              </Button>
              <Button 
                variant="destructive"
                onClick={handleReject}
                disabled={isProcessing}
                className="flex-1"
              >
                <X className="h-4 w-4 mr-2" />
                Reject Registration
              </Button>
            </div>
          )}

          {registration.status !== 'pending' && (
            <Alert>
              <AlertDescription>
                This registration has already been {registration.status}.
                {registration.reviewedDate && ` Reviewed on ${new Date(registration.reviewedDate).toLocaleDateString()}.`}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
