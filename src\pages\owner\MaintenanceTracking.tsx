import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Wrench, 
  Search, 
  Plus,
  MoreHorizontal,
  Eye,
  CheckCircle,
  Clock,
  AlertTriangle,
  XCircle,
  Calendar,
  User,
  Building2,
  Filter,
  TrendingUp,
  TrendingDown,
  ArrowUpRight,
  ArrowDownRight,
  Zap,
  Droplets,
  Wifi,
  AirVent,
  Lightbulb,
  Save,
  X
} from 'lucide-react';
import { mockHostels } from '@/data/mockData';

// Mock maintenance data
interface MaintenanceIssue {
  id: string;
  title: string;
  description: string;
  category: 'electrical' | 'plumbing' | 'hvac' | 'internet' | 'furniture' | 'cleaning' | 'other';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  hostelId: string;
  roomNumber?: string;
  reportedBy: string;
  assignedTo?: string;
  createdDate: string;
  updatedDate: string;
  estimatedCost?: number;
  actualCost?: number;
  completionDate?: string;
}

const mockMaintenanceIssues: MaintenanceIssue[] = [
  {
    id: '1',
    title: 'Air Conditioning Not Working',
    description: 'AC unit in room 101 is not cooling properly. Guests are complaining about the heat.',
    category: 'hvac',
    priority: 'high',
    status: 'in_progress',
    hostelId: '1',
    roomNumber: '101',
    reportedBy: 'Guest - John Doe',
    assignedTo: 'Maintenance Team A',
    createdDate: '2024-01-25T09:00:00Z',
    updatedDate: '2024-01-25T14:30:00Z',
    estimatedCost: 5000,
  },
  {
    id: '2',
    title: 'Leaking Faucet in Common Bathroom',
    description: 'Water is continuously dripping from the faucet in the common bathroom on floor 2.',
    category: 'plumbing',
    priority: 'medium',
    status: 'open',
    hostelId: '1',
    roomNumber: 'Common Area - Floor 2',
    reportedBy: 'Staff - Maintenance Check',
    createdDate: '2024-01-24T16:20:00Z',
    updatedDate: '2024-01-24T16:20:00Z',
    estimatedCost: 1500,
  },
  {
    id: '3',
    title: 'WiFi Connection Issues',
    description: 'Internet connectivity is poor in rooms 201-205. Speed is very slow.',
    category: 'internet',
    priority: 'high',
    status: 'open',
    hostelId: '1',
    roomNumber: '201-205',
    reportedBy: 'Multiple Guests',
    createdDate: '2024-01-24T11:15:00Z',
    updatedDate: '2024-01-24T11:15:00Z',
    estimatedCost: 3000,
  },
  {
    id: '4',
    title: 'Broken Study Table',
    description: 'Study table in room 103 has a broken leg and is unstable.',
    category: 'furniture',
    priority: 'medium',
    status: 'resolved',
    hostelId: '1',
    roomNumber: '103',
    reportedBy: 'Guest - Sarah Wilson',
    assignedTo: 'Maintenance Team B',
    createdDate: '2024-01-22T14:00:00Z',
    updatedDate: '2024-01-23T10:30:00Z',
    completionDate: '2024-01-23T10:30:00Z',
    estimatedCost: 2000,
    actualCost: 1800,
  },
  {
    id: '5',
    title: 'Electrical Outlet Not Working',
    description: 'Power outlet near the bed in room 205 is not functioning.',
    category: 'electrical',
    priority: 'medium',
    status: 'closed',
    hostelId: '1',
    roomNumber: '205',
    reportedBy: 'Guest - Mike Johnson',
    assignedTo: 'Electrician - Raj Kumar',
    createdDate: '2024-01-20T08:45:00Z',
    updatedDate: '2024-01-21T15:20:00Z',
    completionDate: '2024-01-21T15:20:00Z',
    estimatedCost: 800,
    actualCost: 750,
  }
];

export const MaintenanceTracking: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'open' | 'in_progress' | 'resolved' | 'closed'>('all');
  const [priorityFilter, setPriorityFilter] = useState<'all' | 'low' | 'medium' | 'high' | 'urgent'>('all');
  const [categoryFilter, setCategoryFilter] = useState<'all' | 'electrical' | 'plumbing' | 'hvac' | 'internet' | 'furniture' | 'cleaning' | 'other'>('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newIssue, setNewIssue] = useState({
    title: '',
    description: '',
    category: 'other' as MaintenanceIssue['category'],
    priority: 'medium' as MaintenanceIssue['priority'],
    roomNumber: '',
    estimatedCost: ''
  });

  // Get current owner's hostels (assuming owner ID '2' for demo)
  const currentOwnerId = '2';
  const ownerHostels = mockHostels.filter(h => h.ownerId === currentOwnerId);
  
  // Filter issues for owner's hostels
  const ownerIssues = mockMaintenanceIssues.filter(issue => 
    ownerHostels.some(h => h.id === issue.hostelId)
  );

  // Filter issues based on search and filters
  const filteredIssues = ownerIssues.filter(issue => {
    const matchesSearch = issue.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         issue.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         issue.roomNumber?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || issue.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || issue.priority === priorityFilter;
    const matchesCategory = categoryFilter === 'all' || issue.category === categoryFilter;
    
    return matchesSearch && matchesStatus && matchesPriority && matchesCategory;
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      open: 'bg-red-100 text-red-800',
      in_progress: 'bg-yellow-100 text-yellow-800',
      resolved: 'bg-green-100 text-green-800',
      closed: 'bg-gray-100 text-gray-800'
    };
    const icons = {
      open: XCircle,
      in_progress: Clock,
      resolved: CheckCircle,
      closed: CheckCircle
    };
    const Icon = icons[status as keyof typeof icons] || Clock;
    return (
      <Badge className={`${variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800'} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {status.replace('_', ' ').charAt(0).toUpperCase() + status.replace('_', ' ').slice(1)}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const variants = {
      low: 'bg-blue-100 text-blue-800',
      medium: 'bg-yellow-100 text-yellow-800',
      high: 'bg-orange-100 text-orange-800',
      urgent: 'bg-red-100 text-red-800'
    };
    return (
      <Badge className={variants[priority as keyof typeof variants] || 'bg-gray-100 text-gray-800'}>
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </Badge>
    );
  };

  const getCategoryIcon = (category: string) => {
    const icons = {
      electrical: Zap,
      plumbing: Droplets,
      hvac: AirVent,
      internet: Wifi,
      furniture: Building2,
      cleaning: Wrench,
      other: Wrench
    };
    return icons[category as keyof typeof icons] || Wrench;
  };

  const getCategoryLabel = (category: string) => {
    const labels = {
      electrical: 'Electrical',
      plumbing: 'Plumbing',
      hvac: 'HVAC',
      internet: 'Internet',
      furniture: 'Furniture',
      cleaning: 'Cleaning',
      other: 'Other'
    };
    return labels[category as keyof typeof labels] || category;
  };

  const handleCreateIssue = () => {
    // In a real app, this would make an API call
    console.log('Creating maintenance issue:', newIssue);
    setIsCreateDialogOpen(false);
    setNewIssue({
      title: '',
      description: '',
      category: 'other',
      priority: 'medium',
      roomNumber: '',
      estimatedCost: ''
    });
  };

  // Calculate statistics
  const totalIssues = ownerIssues.length;
  const openIssues = ownerIssues.filter(i => i.status === 'open').length;
  const inProgressIssues = ownerIssues.filter(i => i.status === 'in_progress').length;
  const resolvedIssues = ownerIssues.filter(i => i.status === 'resolved').length;
  const urgentIssues = ownerIssues.filter(i => i.priority === 'urgent').length;
  const totalCost = ownerIssues.reduce((sum, i) => sum + (i.actualCost || i.estimatedCost || 0), 0);

  const maintenanceStats = [
    {
      title: 'Total Issues',
      value: totalIssues.toString(),
      change: '+5.2%',
      trend: 'up',
      icon: Wrench,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Open Issues',
      value: openIssues.toString(),
      change: '-2.1%',
      trend: 'down',
      icon: XCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
    {
      title: 'In Progress',
      value: inProgressIssues.toString(),
      change: '+8.3%',
      trend: 'up',
      icon: Clock,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
    },
    {
      title: 'Total Cost',
      value: `₹${(totalCost / 1000).toFixed(0)}K`,
      change: '+12.5%',
      trend: 'up',
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Maintenance & Issues</h1>
          <p className="text-muted-foreground">
            Track and manage property maintenance requests and issues
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Report Issue
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Report New Maintenance Issue</DialogTitle>
              <DialogDescription>
                Create a new maintenance request for your property
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Issue Title</label>
                <Input
                  value={newIssue.title}
                  onChange={(e) => setNewIssue({...newIssue, title: e.target.value})}
                  placeholder="Brief description of the issue"
                />
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium">Category</label>
                  <select 
                    className="w-full p-2 border rounded-md"
                    value={newIssue.category}
                    onChange={(e) => setNewIssue({...newIssue, category: e.target.value as MaintenanceIssue['category']})}
                  >
                    <option value="electrical">Electrical</option>
                    <option value="plumbing">Plumbing</option>
                    <option value="hvac">HVAC</option>
                    <option value="internet">Internet</option>
                    <option value="furniture">Furniture</option>
                    <option value="cleaning">Cleaning</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                <div>
                  <label className="text-sm font-medium">Priority</label>
                  <select 
                    className="w-full p-2 border rounded-md"
                    value={newIssue.priority}
                    onChange={(e) => setNewIssue({...newIssue, priority: e.target.value as MaintenanceIssue['priority']})}
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                    <option value="urgent">Urgent</option>
                  </select>
                </div>
                <div>
                  <label className="text-sm font-medium">Room/Area</label>
                  <Input
                    value={newIssue.roomNumber}
                    onChange={(e) => setNewIssue({...newIssue, roomNumber: e.target.value})}
                    placeholder="e.g., Room 101"
                  />
                </div>
              </div>
              <div>
                <label className="text-sm font-medium">Description</label>
                <Textarea
                  value={newIssue.description}
                  onChange={(e) => setNewIssue({...newIssue, description: e.target.value})}
                  placeholder="Detailed description of the issue"
                  rows={4}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Estimated Cost (₹)</label>
                <Input
                  type="number"
                  value={newIssue.estimatedCost}
                  onChange={(e) => setNewIssue({...newIssue, estimatedCost: e.target.value})}
                  placeholder="Estimated repair cost"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                <X className="mr-2 h-4 w-4" />
                Cancel
              </Button>
              <Button onClick={handleCreateIssue}>
                <Save className="mr-2 h-4 w-4" />
                Create Issue
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Maintenance Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {maintenanceStats.map((stat, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {stat.trend === 'up' ? (
                  <ArrowUpRight className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <ArrowDownRight className="mr-1 h-3 w-3 text-red-500" />
                )}
                <span className={stat.trend === 'up' ? 'text-green-600' : 'text-red-600'}>
                  {stat.change}
                </span>
                <span className="ml-1">from last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Maintenance Issues Table */}
      <Card>
        <CardHeader>
          <CardTitle>Maintenance Issues</CardTitle>
          <CardDescription>
            Track and manage all maintenance requests and issues
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters and Search */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search issues by title, description, or room..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" />
                  Status: {statusFilter === 'all' ? 'All' : statusFilter.replace('_', ' ')}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setStatusFilter('all')}>All</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('open')}>Open</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('in_progress')}>In Progress</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('resolved')}>Resolved</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('closed')}>Closed</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  Priority: {priorityFilter === 'all' ? 'All' : priorityFilter}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>Filter by Priority</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setPriorityFilter('all')}>All</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setPriorityFilter('low')}>Low</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setPriorityFilter('medium')}>Medium</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setPriorityFilter('high')}>High</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setPriorityFilter('urgent')}>Urgent</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Issues Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Issue</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Room/Area</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Cost</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredIssues.map((issue) => {
                  const CategoryIcon = getCategoryIcon(issue.category);
                  return (
                    <TableRow key={issue.id}>
                      <TableCell className="font-medium">
                        <div>
                          <div className="font-semibold">{issue.title}</div>
                          <div className="text-sm text-muted-foreground max-w-xs truncate">
                            {issue.description}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <CategoryIcon className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{getCategoryLabel(issue.category)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Building2 className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">{issue.roomNumber || 'N/A'}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getPriorityBadge(issue.priority)}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(issue.status)}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {issue.actualCost ? (
                            <div>
                              <div className="font-semibold">₹{issue.actualCost.toLocaleString()}</div>
                              <div className="text-muted-foreground">Actual</div>
                            </div>
                          ) : issue.estimatedCost ? (
                            <div>
                              <div className="font-semibold">₹{issue.estimatedCost.toLocaleString()}</div>
                              <div className="text-muted-foreground">Estimated</div>
                            </div>
                          ) : (
                            <span className="text-muted-foreground">N/A</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">
                            {new Date(issue.createdDate).toLocaleDateString()}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {issue.status === 'open' && (
                              <DropdownMenuItem>
                                <Clock className="mr-2 h-4 w-4" />
                                Start Work
                              </DropdownMenuItem>
                            )}
                            {issue.status === 'in_progress' && (
                              <DropdownMenuItem>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Mark Resolved
                              </DropdownMenuItem>
                            )}
                            {issue.priority !== 'urgent' && (
                              <DropdownMenuItem className="text-red-600">
                                <AlertTriangle className="mr-2 h-4 w-4" />
                                Mark Urgent
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>

          {filteredIssues.length === 0 && (
            <div className="text-center py-8">
              <Wrench className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No maintenance issues found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search criteria or create a new issue.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
