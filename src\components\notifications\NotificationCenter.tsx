import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  <PERSON>, 
  Check, 
  X, 
  Eye,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON>,
  Filter
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/contexts/AuthContext';
import { 
  NotificationService, 
  Notification,
  formatNotificationTime,
  getPriorityColor,
  getTypeIcon
} from '@/utils/notificationService';

interface NotificationCenterProps {
  showAsDropdown?: boolean;
}

export const NotificationCenter: React.FC<NotificationCenterProps> = ({ 
  showAsDropdown = true 
}) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [filter, setFilter] = useState<'all' | 'unread' | 'high'>('all');

  useEffect(() => {
    if (user) {
      loadNotifications();
    }
  }, [user]);

  const loadNotifications = () => {
    if (!user) return;
    
    const userNotifications = NotificationService.getNotificationsForUser(user.id);
    setNotifications(userNotifications);
    setUnreadCount(NotificationService.getUnreadCount(user.id));
  };

  const handleNotificationClick = (notification: Notification) => {
    if (notification.status === 'unread') {
      NotificationService.markAsRead(notification.id);
      loadNotifications();
    }
    
    if (notification.actionUrl) {
      navigate(notification.actionUrl);
    }
  };

  const markAsRead = (notificationId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    NotificationService.markAsRead(notificationId);
    loadNotifications();
  };

  const markAllAsRead = () => {
    if (user) {
      NotificationService.markAllAsRead(user.id);
      loadNotifications();
    }
  };

  const deleteNotification = (notificationId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    NotificationService.deleteNotification(notificationId);
    loadNotifications();
  };

  const getFilteredNotifications = () => {
    switch (filter) {
      case 'unread':
        return notifications.filter(n => n.status === 'unread');
      case 'high':
        return notifications.filter(n => n.priority === 'high');
      default:
        return notifications;
    }
  };

  const filteredNotifications = getFilteredNotifications();

  if (showAsDropdown) {
    return (
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="ghost" size="sm" className="relative">
            <Bell className="h-5 w-5" />
            {unreadCount > 0 && (
              <Badge 
                variant="destructive" 
                className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
              >
                {unreadCount > 99 ? '99+' : unreadCount}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-96 p-0" align="end">
          <NotificationList 
            notifications={filteredNotifications}
            filter={filter}
            setFilter={setFilter}
            onNotificationClick={handleNotificationClick}
            onMarkAsRead={markAsRead}
            onDelete={deleteNotification}
            onMarkAllAsRead={markAllAsRead}
            unreadCount={unreadCount}
          />
        </PopoverContent>
      </Popover>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Notifications
          {unreadCount > 0 && (
            <Badge variant="destructive">{unreadCount}</Badge>
          )}
        </CardTitle>
        <CardDescription>
          Stay updated with your hostel management activities
        </CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        <NotificationList 
          notifications={filteredNotifications}
          filter={filter}
          setFilter={setFilter}
          onNotificationClick={handleNotificationClick}
          onMarkAsRead={markAsRead}
          onDelete={deleteNotification}
          onMarkAllAsRead={markAllAsRead}
          unreadCount={unreadCount}
        />
      </CardContent>
    </Card>
  );
};

interface NotificationListProps {
  notifications: Notification[];
  filter: 'all' | 'unread' | 'high';
  setFilter: (filter: 'all' | 'unread' | 'high') => void;
  onNotificationClick: (notification: Notification) => void;
  onMarkAsRead: (notificationId: string, event: React.MouseEvent) => void;
  onDelete: (notificationId: string, event: React.MouseEvent) => void;
  onMarkAllAsRead: () => void;
  unreadCount: number;
}

const NotificationList: React.FC<NotificationListProps> = ({
  notifications,
  filter,
  setFilter,
  onNotificationClick,
  onMarkAsRead,
  onDelete,
  onMarkAllAsRead,
  unreadCount
}) => {
  return (
    <div className="max-h-96 overflow-y-auto">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold">Notifications</h3>
          {unreadCount > 0 && (
            <Button variant="ghost" size="sm" onClick={onMarkAllAsRead}>
              <CheckCheck className="h-4 w-4 mr-1" />
              Mark all read
            </Button>
          )}
        </div>
        
        {/* Filter */}
        <div className="flex gap-2">
          <Button 
            variant={filter === 'all' ? 'default' : 'ghost'} 
            size="sm"
            onClick={() => setFilter('all')}
          >
            All
          </Button>
          <Button 
            variant={filter === 'unread' ? 'default' : 'ghost'} 
            size="sm"
            onClick={() => setFilter('unread')}
          >
            Unread
          </Button>
          <Button 
            variant={filter === 'high' ? 'default' : 'ghost'} 
            size="sm"
            onClick={() => setFilter('high')}
          >
            High Priority
          </Button>
        </div>
      </div>

      {/* Notifications */}
      <div className="divide-y">
        {notifications.length === 0 ? (
          <div className="p-8 text-center text-muted-foreground">
            <Bell className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No notifications found</p>
          </div>
        ) : (
          notifications.map((notification) => (
            <NotificationItem
              key={notification.id}
              notification={notification}
              onClick={() => onNotificationClick(notification)}
              onMarkAsRead={onMarkAsRead}
              onDelete={onDelete}
            />
          ))
        )}
      </div>
    </div>
  );
};

interface NotificationItemProps {
  notification: Notification;
  onClick: () => void;
  onMarkAsRead: (notificationId: string, event: React.MouseEvent) => void;
  onDelete: (notificationId: string, event: React.MouseEvent) => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onClick,
  onMarkAsRead,
  onDelete
}) => {
  return (
    <div 
      className={`p-4 hover:bg-muted/50 cursor-pointer transition-colors ${
        notification.status === 'unread' ? 'bg-blue-50/50' : ''
      }`}
      onClick={onClick}
    >
      <div className="flex items-start gap-3">
        <div className="text-lg">{getTypeIcon(notification.type)}</div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1">
              <h4 className={`text-sm font-medium ${
                notification.status === 'unread' ? 'text-foreground' : 'text-muted-foreground'
              }`}>
                {notification.title}
              </h4>
              <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                {notification.message}
              </p>
              <div className="flex items-center gap-2 mt-2">
                <span className="text-xs text-muted-foreground">
                  {formatNotificationTime(notification.createdAt)}
                </span>
                <Badge 
                  variant="outline" 
                  className={`text-xs ${getPriorityColor(notification.priority)}`}
                >
                  {notification.priority}
                </Badge>
              </div>
            </div>
            
            <div className="flex items-center gap-1">
              {notification.status === 'unread' && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => onMarkAsRead(notification.id, e)}
                  className="h-6 w-6 p-0"
                >
                  <Check className="h-3 w-3" />
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => onDelete(notification.id, e)}
                className="h-6 w-6 p-0 text-destructive hover:text-destructive"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
