import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Shield, 
  Search, 
  Download,
  MoreHorizontal,
  Eye,
  Calendar,
  User,
  Settings,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Filter,
  Activity,
  Database,
  Lock,
  Unlock,
  Edit,
  Trash2,
  Plus
} from 'lucide-react';
import { mockUsers, getUserById } from '@/data/mockData';

// Mock audit log data
interface AuditLog {
  id: string;
  userId: string;
  action: string;
  resource: string;
  resourceId?: string;
  details: string;
  ipAddress: string;
  userAgent: string;
  timestamp: string;
  status: 'success' | 'failed' | 'warning';
  severity: 'low' | 'medium' | 'high' | 'critical';
}

const mockAuditLogs: AuditLog[] = [
  {
    id: '1',
    userId: '1',
    action: 'USER_CREATED',
    resource: 'User',
    resourceId: '15',
    details: 'Created new user account for John Doe',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    timestamp: '2024-01-25T10:30:00Z',
    status: 'success',
    severity: 'low'
  },
  {
    id: '2',
    userId: '1',
    action: 'HOSTEL_APPROVED',
    resource: 'Hostel',
    resourceId: '5',
    details: 'Approved hostel registration for Green Valley Hostel',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    timestamp: '2024-01-25T09:15:00Z',
    status: 'success',
    severity: 'medium'
  },
  {
    id: '3',
    userId: '1',
    action: 'PAYMENT_REFUND',
    resource: 'Payment',
    resourceId: '12',
    details: 'Processed refund of ₹25,000 for booking cancellation',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    timestamp: '2024-01-25T08:45:00Z',
    status: 'success',
    severity: 'high'
  },
  {
    id: '4',
    userId: '1',
    action: 'LOGIN_FAILED',
    resource: 'Authentication',
    details: 'Failed login attempt - invalid credentials',
    ipAddress: '************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    timestamp: '2024-01-25T07:20:00Z',
    status: 'failed',
    severity: 'medium'
  },
  {
    id: '5',
    userId: '1',
    action: 'SYSTEM_SETTINGS_UPDATED',
    resource: 'Settings',
    details: 'Updated platform commission rate from 5% to 5.5%',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    timestamp: '2024-01-24T16:30:00Z',
    status: 'success',
    severity: 'critical'
  },
  {
    id: '6',
    userId: '2',
    action: 'USER_SUSPENDED',
    resource: 'User',
    resourceId: '8',
    details: 'Suspended user account due to policy violation',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    timestamp: '2024-01-24T14:15:00Z',
    status: 'success',
    severity: 'high'
  }
];

export const AuditLogs: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'success' | 'failed' | 'warning'>('all');
  const [severityFilter, setSeverityFilter] = useState<'all' | 'low' | 'medium' | 'high' | 'critical'>('all');
  const [actionFilter, setActionFilter] = useState<'all' | 'auth' | 'user' | 'hostel' | 'payment' | 'system'>('all');

  // Filter logs based on search and filters
  const filteredLogs = mockAuditLogs.filter(log => {
    const user = getUserById(log.userId);
    const matchesSearch = log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.details.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.resource.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || log.status === statusFilter;
    const matchesSeverity = severityFilter === 'all' || log.severity === severityFilter;
    
    let matchesAction = true;
    if (actionFilter !== 'all') {
      const actionCategories = {
        auth: ['LOGIN', 'LOGOUT', 'LOGIN_FAILED', 'PASSWORD_RESET'],
        user: ['USER_CREATED', 'USER_UPDATED', 'USER_SUSPENDED', 'USER_DELETED'],
        hostel: ['HOSTEL_CREATED', 'HOSTEL_APPROVED', 'HOSTEL_REJECTED', 'HOSTEL_UPDATED'],
        payment: ['PAYMENT_PROCESSED', 'PAYMENT_REFUND', 'PAYMENT_FAILED'],
        system: ['SYSTEM_SETTINGS_UPDATED', 'BACKUP_CREATED', 'MAINTENANCE_MODE']
      };
      matchesAction = actionCategories[actionFilter as keyof typeof actionCategories]?.some(action => 
        log.action.includes(action)
      ) || false;
    }
    
    return matchesSearch && matchesStatus && matchesSeverity && matchesAction;
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      success: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800',
      warning: 'bg-yellow-100 text-yellow-800'
    };
    const icons = {
      success: CheckCircle,
      failed: XCircle,
      warning: AlertTriangle
    };
    const Icon = icons[status as keyof typeof icons] || Clock;
    return (
      <Badge className={`${variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800'} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getSeverityBadge = (severity: string) => {
    const variants = {
      low: 'bg-blue-100 text-blue-800',
      medium: 'bg-yellow-100 text-yellow-800',
      high: 'bg-orange-100 text-orange-800',
      critical: 'bg-red-100 text-red-800'
    };
    return (
      <Badge className={variants[severity as keyof typeof variants] || 'bg-gray-100 text-gray-800'}>
        {severity.charAt(0).toUpperCase() + severity.slice(1)}
      </Badge>
    );
  };

  const getActionIcon = (action: string) => {
    if (action.includes('LOGIN') || action.includes('AUTH')) return Lock;
    if (action.includes('USER')) return User;
    if (action.includes('HOSTEL')) return Settings;
    if (action.includes('PAYMENT')) return Database;
    if (action.includes('SYSTEM')) return Settings;
    return Activity;
  };

  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Calculate statistics
  const totalLogs = mockAuditLogs.length;
  const successfulActions = mockAuditLogs.filter(log => log.status === 'success').length;
  const failedActions = mockAuditLogs.filter(log => log.status === 'failed').length;
  const criticalActions = mockAuditLogs.filter(log => log.severity === 'critical').length;

  const auditStats = [
    {
      title: 'Total Actions',
      value: totalLogs.toString(),
      icon: Activity,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Successful',
      value: successfulActions.toString(),
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Failed',
      value: failedActions.toString(),
      icon: XCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
    {
      title: 'Critical',
      value: criticalActions.toString(),
      icon: AlertTriangle,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Audit Logs</h1>
          <p className="text-muted-foreground">
            Track all system activities and administrative actions
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export Logs
          </Button>
          <Button>
            <Shield className="mr-2 h-4 w-4" />
            Security Report
          </Button>
        </div>
      </div>

      {/* Audit Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {auditStats.map((stat, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                Last 24 hours
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Audit Logs Table */}
      <Card>
        <CardHeader>
          <CardTitle>System Activity Log</CardTitle>
          <CardDescription>
            Detailed log of all administrative and user activities
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters and Search */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by action, user, or details..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" />
                  Status: {statusFilter === 'all' ? 'All' : statusFilter}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setStatusFilter('all')}>All</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('success')}>Success</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('failed')}>Failed</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('warning')}>Warning</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  Severity: {severityFilter === 'all' ? 'All' : severityFilter}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>Filter by Severity</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setSeverityFilter('all')}>All</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSeverityFilter('low')}>Low</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSeverityFilter('medium')}>Medium</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSeverityFilter('high')}>High</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSeverityFilter('critical')}>Critical</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Audit Logs Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Action</TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Resource</TableHead>
                  <TableHead>Details</TableHead>
                  <TableHead>Timestamp</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Severity</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredLogs.map((log) => {
                  const user = getUserById(log.userId);
                  const ActionIcon = getActionIcon(log.action);
                  return (
                    <TableRow key={log.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-2">
                          <ActionIcon className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{log.action.replace(/_/g, ' ')}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={user?.avatar} alt={user?.name} />
                            <AvatarFallback className="text-xs">
                              {user ? getUserInitials(user.name) : 'SY'}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="text-sm font-medium">{user?.name || 'System'}</div>
                            <div className="text-xs text-muted-foreground">{user?.email}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {log.resource}
                          {log.resourceId && (
                            <span className="text-muted-foreground"> #{log.resourceId}</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm max-w-xs truncate" title={log.details}>
                          {log.details}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">
                            {new Date(log.timestamp).toLocaleString()}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(log.status)}
                      </TableCell>
                      <TableCell>
                        {getSeverityBadge(log.severity)}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Download className="mr-2 h-4 w-4" />
                              Export Entry
                            </DropdownMenuItem>
                            {log.severity === 'critical' && (
                              <>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-red-600">
                                  <AlertTriangle className="mr-2 h-4 w-4" />
                                  Flag for Review
                                </DropdownMenuItem>
                              </>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>

          {filteredLogs.length === 0 && (
            <div className="text-center py-8">
              <Shield className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No audit logs found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search criteria or filters.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
