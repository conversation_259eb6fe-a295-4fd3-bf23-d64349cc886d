import React, { useState } from 'react';
import { 
  History, 
  Calendar, 
  User, 
  Clock, 
  CheckCircle, 
  XCircle,
  AlertTriangle,
  Filter,
  Search,
  Eye,
  Download
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  BedAllocation, 
  HostelTenant,
  Bed,
  Room,
  mockBedAllocations,
  mockHostelTenants,
  mockBeds,
  mockRooms,
  getHostelTenantById,
  getBedById,
  getAllocationsByHostelId,
  getActiveAllocationsByHostelId
} from '@/data/mockData';

interface AllocationHistoryProps {
  hostelId: string;
}

export const AllocationHistory: React.FC<AllocationHistoryProps> = ({ hostelId }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'terminated' | 'expired'>('all');
  const [selectedAllocation, setSelectedAllocation] = useState<BedAllocation | null>(null);

  // Get allocations for the hostel
  const allAllocations = getAllocationsByHostelId(hostelId);
  const activeAllocations = getActiveAllocationsByHostelId(hostelId);

  // Filter allocations
  const filteredAllocations = allAllocations.filter(allocation => {
    const tenant = getHostelTenantById(allocation.tenantId);
    const bed = getBedById(allocation.bedId);
    const room = mockRooms.find(r => r.id === allocation.roomId);
    
    const matchesSearch = tenant && (
      tenant.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tenant.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tenant.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      bed?.bedNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      room?.roomNumber.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    const matchesStatus = statusFilter === 'all' || allocation.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  // Get upcoming events (check-ins and check-outs in next 7 days)
  const getUpcomingEvents = () => {
    const today = new Date();
    const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
    
    const events: Array<{
      type: 'check-in' | 'check-out';
      date: string;
      allocation: BedAllocation;
      tenant: HostelTenant;
      bed: Bed;
      room: Room;
    }> = [];

    activeAllocations.forEach(allocation => {
      const tenant = getHostelTenantById(allocation.tenantId);
      const bed = getBedById(allocation.bedId);
      const room = mockRooms.find(r => r.id === allocation.roomId);
      
      if (!tenant || !bed || !room) return;

      // Check for upcoming check-outs
      const checkOutDate = new Date(allocation.expectedCheckOutDate);
      if (checkOutDate >= today && checkOutDate <= nextWeek) {
        events.push({
          type: 'check-out',
          date: allocation.expectedCheckOutDate,
          allocation,
          tenant,
          bed,
          room
        });
      }
    });

    return events.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  };

  const upcomingEvents = getUpcomingEvents();

  const getStatusColor = (status: BedAllocation['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'terminated':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'expired':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'pending':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: BedAllocation['status']) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-3 w-3" />;
      case 'terminated':
        return <XCircle className="h-3 w-3" />;
      case 'expired':
        return <AlertTriangle className="h-3 w-3" />;
      case 'pending':
        return <Clock className="h-3 w-3" />;
      default:
        return <Clock className="h-3 w-3" />;
    }
  };

  const calculateStayDuration = (allocation: BedAllocation) => {
    const checkInDate = new Date(allocation.checkInDate);
    const endDate = allocation.actualCheckOutDate 
      ? new Date(allocation.actualCheckOutDate)
      : new Date(allocation.expectedCheckOutDate);
    
    const diffTime = Math.abs(endDate.getTime() - checkInDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="history" className="space-y-4">
        <TabsList>
          <TabsTrigger value="history">Allocation History</TabsTrigger>
          <TabsTrigger value="upcoming">Upcoming Events</TabsTrigger>
        </TabsList>

        <TabsContent value="history" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      placeholder="Search by tenant name, bed, or room..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="w-full md:w-48">
                  <Select value={statusFilter} onValueChange={(value: any) => setStatusFilter(value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="terminated">Terminated</SelectItem>
                      <SelectItem value="expired">Expired</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Allocation History Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <History className="h-5 w-5" />
                Allocation History
              </CardTitle>
              <CardDescription>
                Complete history of bed allocations for this hostel
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Tenant</TableHead>
                      <TableHead>Bed/Room</TableHead>
                      <TableHead>Duration</TableHead>
                      <TableHead>Rent</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Check-in</TableHead>
                      <TableHead>Check-out</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAllocations.map((allocation) => {
                      const tenant = getHostelTenantById(allocation.tenantId);
                      const bed = getBedById(allocation.bedId);
                      const room = mockRooms.find(r => r.id === allocation.roomId);
                      
                      if (!tenant || !bed || !room) return null;

                      return (
                        <TableRow key={allocation.id}>
                          <TableCell>
                            <div className="space-y-1">
                              <p className="font-medium">{tenant.firstName} {tenant.lastName}</p>
                              <p className="text-sm text-muted-foreground">{tenant.phone}</p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <p className="font-medium">{bed.bedNumber}</p>
                              <p className="text-sm text-muted-foreground">{room.roomNumber}</p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <p className="text-sm">{calculateStayDuration(allocation)} days</p>
                          </TableCell>
                          <TableCell>
                            <p className="font-medium">₹{allocation.monthlyRent.toLocaleString()}</p>
                          </TableCell>
                          <TableCell>
                            <Badge 
                              variant="outline" 
                              className={`flex items-center gap-1 w-fit ${getStatusColor(allocation.status)}`}
                            >
                              {getStatusIcon(allocation.status)}
                              {allocation.status.charAt(0).toUpperCase() + allocation.status.slice(1)}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <p className="text-sm">
                              {new Date(allocation.checkInDate).toLocaleDateString()}
                            </p>
                          </TableCell>
                          <TableCell>
                            <p className="text-sm">
                              {allocation.actualCheckOutDate 
                                ? new Date(allocation.actualCheckOutDate).toLocaleDateString()
                                : new Date(allocation.expectedCheckOutDate).toLocaleDateString()
                              }
                            </p>
                          </TableCell>
                          <TableCell className="text-right">
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => setSelectedAllocation(allocation)}
                                >
                                  <Eye className="h-4 w-4 mr-1" />
                                  View
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="max-w-2xl">
                                <DialogHeader>
                                  <DialogTitle>Allocation Details</DialogTitle>
                                  <DialogDescription>
                                    Complete information about this bed allocation
                                  </DialogDescription>
                                </DialogHeader>
                                
                                {selectedAllocation && (
                                  <AllocationDetailsView allocation={selectedAllocation} />
                                )}
                              </DialogContent>
                            </Dialog>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>

              {filteredAllocations.length === 0 && (
                <div className="text-center py-8">
                  <History className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No allocation history found</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="upcoming" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Upcoming Events
              </CardTitle>
              <CardDescription>
                Check-ins and check-outs scheduled for the next 7 days
              </CardDescription>
            </CardHeader>
            <CardContent>
              {upcomingEvents.length === 0 ? (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No upcoming events in the next 7 days</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {upcomingEvents.map((event, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                          event.type === 'check-in' 
                            ? 'bg-green-100 text-green-600' 
                            : 'bg-red-100 text-red-600'
                        }`}>
                          {event.type === 'check-in' ? <User className="h-5 w-5" /> : <Clock className="h-5 w-5" />}
                        </div>
                        <div>
                          <p className="font-medium">
                            {event.tenant.firstName} {event.tenant.lastName}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {event.type === 'check-in' ? 'Check-in' : 'Expected Check-out'} • 
                            Bed {event.bed.bedNumber} • {event.room.roomNumber}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{new Date(event.date).toLocaleDateString()}</p>
                        <p className="text-sm text-muted-foreground">
                          {Math.ceil((new Date(event.date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} days
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Allocation Details View Component
interface AllocationDetailsViewProps {
  allocation: BedAllocation;
}

const AllocationDetailsView: React.FC<AllocationDetailsViewProps> = ({ allocation }) => {
  const tenant = getHostelTenantById(allocation.tenantId);
  const bed = getBedById(allocation.bedId);
  const room = mockRooms.find(r => r.id === allocation.roomId);

  if (!tenant || !bed || !room) return null;

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="text-sm font-medium text-muted-foreground">Tenant</p>
          <p className="font-medium">{tenant.firstName} {tenant.lastName}</p>
          <p className="text-sm text-muted-foreground">{tenant.email}</p>
        </div>
        <div>
          <p className="text-sm font-medium text-muted-foreground">Bed/Room</p>
          <p className="font-medium">Bed {bed.bedNumber}</p>
          <p className="text-sm text-muted-foreground">{room.roomNumber}</p>
        </div>
        <div>
          <p className="text-sm font-medium text-muted-foreground">Check-in Date</p>
          <p>{new Date(allocation.checkInDate).toLocaleDateString()}</p>
        </div>
        <div>
          <p className="text-sm font-medium text-muted-foreground">Expected Check-out</p>
          <p>{new Date(allocation.expectedCheckOutDate).toLocaleDateString()}</p>
        </div>
        <div>
          <p className="text-sm font-medium text-muted-foreground">Monthly Rent</p>
          <p className="font-medium">₹{allocation.monthlyRent.toLocaleString()}</p>
        </div>
        <div>
          <p className="text-sm font-medium text-muted-foreground">Security Deposit</p>
          <p className="font-medium">₹{allocation.securityDeposit.toLocaleString()}</p>
        </div>
      </div>
      
      {allocation.allocationNotes && (
        <div>
          <p className="text-sm font-medium text-muted-foreground">Notes</p>
          <p className="text-sm bg-muted p-2 rounded">{allocation.allocationNotes}</p>
        </div>
      )}
    </div>
  );
};
