import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Building2, 
  <PERSON><PERSON><PERSON>cle, 
  ArrowLeft,
  Users,
  Shield,
  TrendingUp,
  Clock
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AccountRequestForm } from '@/components/forms/AccountRequestForm';
import { AccountRequest, mockAccountRequests } from '@/data/mockData';

export const AccountRequestPage: React.FC = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (
    data: Omit<AccountRequest, 'id' | 'status' | 'submittedDate' | 'documents'>, 
    documents: FileList
  ) => {
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Create document paths (in real app, files would be uploaded to server)
      const documentPaths = {
        identityProof: `/documents/${data.firstName.toLowerCase()}-identity.pdf`,
        addressProof: `/documents/${data.firstName.toLowerCase()}-address.pdf`,
        businessLicense: `/documents/${data.businessName.toLowerCase().replace(/\s+/g, '-')}-license.pdf`,
        panCard: `/documents/${data.firstName.toLowerCase()}-pan.pdf`,
        gstCertificate: data.gstNumber ? `/documents/${data.businessName.toLowerCase().replace(/\s+/g, '-')}-gst.pdf` : undefined
      };

      const newAccountRequest: AccountRequest = {
        ...data,
        id: `acc-req-${Date.now()}`,
        documents: documentPaths,
        status: 'pending',
        submittedDate: new Date().toISOString().split('T')[0],
      };

      // Add to mock data (in real app, this would be handled by the API)
      mockAccountRequests.push(newAccountRequest);

      setIsSubmitted(true);

    } catch (error) {
      alert('There was an error submitting your request. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="text-center py-12">
            <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-6" />
            <h1 className="text-2xl font-bold text-green-800 mb-4">
              Account Request Submitted Successfully!
            </h1>
            <p className="text-muted-foreground mb-6">
              Thank you for your interest in joining Room Buddy Hub. Your account request has been submitted 
              and is now under review by our admin team.
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <h3 className="font-semibold text-blue-800 mb-2">What happens next?</h3>
              <ul className="text-sm text-blue-700 space-y-1 text-left">
                <li>• Our team will review your application within 3-5 business days</li>
                <li>• We'll verify your documents and business information</li>
                <li>• You'll receive an email notification about the approval status</li>
                <li>• If approved, you'll get login credentials to access your account</li>
              </ul>
            </div>
            <div className="flex gap-4 justify-center">
              <Button onClick={() => navigate('/')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Home
              </Button>
              <Button variant="outline" onClick={() => navigate('/login')}>
                Already have an account? Login
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <Building2 className="h-8 w-8 text-primary" />
              <span className="text-xl font-bold text-gray-900">Room Buddy Hub</span>
            </div>
            <div className="flex items-center gap-4">
              <Button variant="outline" onClick={() => navigate('/')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Home
              </Button>
              <Button variant="outline" onClick={() => navigate('/login')}>
                Login
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Information */}
          <div className="lg:col-span-1 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-xl">Join Room Buddy Hub</CardTitle>
                <CardDescription>
                  Become a partner and grow your hospitality business with us
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start gap-3">
                  <Users className="h-5 w-5 text-blue-600 mt-1" />
                  <div>
                    <h4 className="font-medium">Reach More Customers</h4>
                    <p className="text-sm text-muted-foreground">
                      Connect with students and professionals looking for quality accommodation
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <Shield className="h-5 w-5 text-green-600 mt-1" />
                  <div>
                    <h4 className="font-medium">Secure Platform</h4>
                    <p className="text-sm text-muted-foreground">
                      Safe and secure booking system with verified users
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <TrendingUp className="h-5 w-5 text-purple-600 mt-1" />
                  <div>
                    <h4 className="font-medium">Grow Your Business</h4>
                    <p className="text-sm text-muted-foreground">
                      Advanced analytics and management tools to optimize your operations
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Application Process
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                      1
                    </div>
                    <span className="text-sm">Submit your application with required documents</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                      2
                    </div>
                    <span className="text-sm">Our team reviews your application (3-5 days)</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                      3
                    </div>
                    <span className="text-sm">Get approved and receive login credentials</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                      4
                    </div>
                    <span className="text-sm">Start registering your hostels and accepting bookings</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Alert>
              <AlertDescription>
                <strong>Note:</strong> All information provided will be verified. Please ensure 
                all documents are clear and up-to-date. False information may result in 
                application rejection.
              </AlertDescription>
            </Alert>
          </div>

          {/* Right Column - Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Account Request Application</CardTitle>
                <CardDescription>
                  Fill out the form below to request a hostel owner account
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AccountRequestForm 
                  onSubmit={handleSubmit}
                  isLoading={isSubmitting}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};
