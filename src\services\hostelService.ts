// Hostel Service - API layer for hostel management operations

import { 
  Hostel, 
  HostelRegistration, 
  Floor, 
  Room,
  mockHostels,
  mockHostelRegistrations,
  mockFloors,
  mockRooms
} from '@/data/mockData';
import { NotificationService, StatusTracker } from '@/utils/notificationService';

// Base API response interface
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Hostel Registration Service
export class HostelRegistrationService {
  // Submit new hostel registration
  static async submitRegistration(
    registrationData: Omit<HostelRegistration, 'id' | 'status' | 'submittedDate'>
  ): Promise<ApiResponse<HostelRegistration>> {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newRegistration: HostelRegistration = {
        ...registrationData,
        id: `reg-${Date.now()}`,
        status: 'pending',
        submittedDate: new Date().toISOString().split('T')[0],
      };

      mockHostelRegistrations.push(newRegistration);

      // Notify admin of new registration
      NotificationService.notifyAdminOfNewRegistration(
        newRegistration.id,
        newRegistration.ownerName,
        newRegistration.name
      );

      return {
        success: true,
        data: newRegistration,
        message: 'Hostel registration submitted successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to submit hostel registration'
      };
    }
  }

  // Get registrations by owner ID
  static async getRegistrationsByOwner(ownerId: string): Promise<ApiResponse<HostelRegistration[]>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));

      const registrations = mockHostelRegistrations.filter(reg => reg.ownerId === ownerId);
      
      return {
        success: true,
        data: registrations
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to fetch registrations'
      };
    }
  }

  // Get all pending registrations (admin only)
  static async getPendingRegistrations(): Promise<ApiResponse<HostelRegistration[]>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));

      const pendingRegistrations = mockHostelRegistrations.filter(reg => reg.status === 'pending');
      
      return {
        success: true,
        data: pendingRegistrations
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to fetch pending registrations'
      };
    }
  }

  // Approve hostel registration (admin only)
  static async approveRegistration(
    registrationId: string,
    adminId: string,
    comments?: string
  ): Promise<ApiResponse<HostelRegistration>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const registration = mockHostelRegistrations.find(reg => reg.id === registrationId);
      if (!registration) {
        return {
          success: false,
          error: 'Registration not found'
        };
      }

      const oldStatus = registration.status;
      registration.status = 'approved';
      registration.reviewedDate = new Date().toISOString().split('T')[0];
      registration.reviewedBy = adminId;
      registration.reviewComments = comments;

      // Track status change
      StatusTracker.trackRegistrationStatusChange(
        registrationId,
        oldStatus,
        'approved',
        adminId,
        comments
      );

      // Notify owner of approval
      NotificationService.notifyOwnerOfApprovalStatus(
        registration.ownerId,
        registration.name,
        'approved'
      );

      // Create approved hostel entry
      const approvedHostel: Hostel = {
        id: `hostel-${Date.now()}`,
        name: registration.name,
        address: registration.address,
        city: registration.city,
        state: registration.state,
        pincode: registration.pincode,
        totalBeds: registration.totalBeds,
        availableBeds: registration.totalBeds, // Initially all beds are available
        pricePerBed: registration.pricePerBed,
        amenities: registration.amenities,
        rating: 0, // Initial rating
        images: registration.images,
        ownerId: registration.ownerId,
        employees: [],
        status: 'active',
        approvalStatus: 'approved',
        createdDate: new Date().toISOString().split('T')[0],
        approvedDate: new Date().toISOString().split('T')[0]
      };

      mockHostels.push(approvedHostel);

      return {
        success: true,
        data: registration,
        message: 'Hostel registration approved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to approve registration'
      };
    }
  }

  // Reject hostel registration (admin only)
  static async rejectRegistration(
    registrationId: string,
    adminId: string,
    rejectionReason: string,
    comments?: string
  ): Promise<ApiResponse<HostelRegistration>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const registration = mockHostelRegistrations.find(reg => reg.id === registrationId);
      if (!registration) {
        return {
          success: false,
          error: 'Registration not found'
        };
      }

      const oldStatus = registration.status;
      registration.status = 'rejected';
      registration.reviewedDate = new Date().toISOString().split('T')[0];
      registration.reviewedBy = adminId;
      registration.rejectionReason = rejectionReason;
      registration.reviewComments = comments;

      // Track status change
      StatusTracker.trackRegistrationStatusChange(
        registrationId,
        oldStatus,
        'rejected',
        adminId,
        rejectionReason
      );

      // Notify owner of rejection
      NotificationService.notifyOwnerOfApprovalStatus(
        registration.ownerId,
        registration.name,
        'rejected',
        rejectionReason
      );

      return {
        success: true,
        data: registration,
        message: 'Hostel registration rejected'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to reject registration'
      };
    }
  }
}

// Hostel Management Service
export class HostelService {
  // Get hostels by owner ID
  static async getHostelsByOwner(ownerId: string): Promise<ApiResponse<Hostel[]>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));

      const hostels = mockHostels.filter(hostel => 
        hostel.ownerId === ownerId && hostel.approvalStatus === 'approved'
      );
      
      return {
        success: true,
        data: hostels
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to fetch hostels'
      };
    }
  }

  // Get all hostels (admin only)
  static async getAllHostels(): Promise<ApiResponse<Hostel[]>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      return {
        success: true,
        data: mockHostels
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to fetch hostels'
      };
    }
  }

  // Update hostel status
  static async updateHostelStatus(
    hostelId: string,
    status: 'active' | 'inactive',
    updatedBy: string
  ): Promise<ApiResponse<Hostel>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));

      const hostel = mockHostels.find(h => h.id === hostelId);
      if (!hostel) {
        return {
          success: false,
          error: 'Hostel not found'
        };
      }

      hostel.status = status;

      return {
        success: true,
        data: hostel,
        message: `Hostel status updated to ${status}`
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to update hostel status'
      };
    }
  }
}

// Room Management Service
export class RoomService {
  // Create new room
  static async createRoom(
    roomData: Omit<Room, 'id' | 'currentOccupancy' | 'status' | 'images' | 'createdDate' | 'updatedDate'>
  ): Promise<ApiResponse<Room>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));

      const newRoom: Room = {
        ...roomData,
        id: `room-${Date.now()}`,
        currentOccupancy: 0,
        status: 'available',
        images: [],
        createdDate: new Date().toISOString().split('T')[0],
        updatedDate: new Date().toISOString().split('T')[0]
      };

      mockRooms.push(newRoom);

      // Update floor's total rooms count
      const floor = mockFloors.find(f => f.id === roomData.floorId);
      if (floor) {
        floor.totalRooms += 1;
      }

      // Track room creation
      StatusTracker.trackFloorRoomChange(
        'room',
        newRoom.id,
        'created',
        'owner', // In real app, this would be the actual user ID
        { roomNumber: newRoom.roomNumber, roomType: newRoom.roomType }
      );

      return {
        success: true,
        data: newRoom,
        message: 'Room created successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to create room'
      };
    }
  }

  // Get rooms by hostel ID
  static async getRoomsByHostel(hostelId: string): Promise<ApiResponse<Room[]>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));

      const rooms = mockRooms.filter(room => room.hostelId === hostelId);

      return {
        success: true,
        data: rooms
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to fetch rooms'
      };
    }
  }

  // Get rooms by floor ID
  static async getRoomsByFloor(floorId: string): Promise<ApiResponse<Room[]>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));

      const rooms = mockRooms.filter(room => room.floorId === floorId);

      return {
        success: true,
        data: rooms
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to fetch rooms'
      };
    }
  }

  // Update room
  static async updateRoom(
    roomId: string,
    updateData: Partial<Room>
  ): Promise<ApiResponse<Room>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));

      const roomIndex = mockRooms.findIndex(room => room.id === roomId);
      if (roomIndex === -1) {
        return {
          success: false,
          error: 'Room not found'
        };
      }

      mockRooms[roomIndex] = {
        ...mockRooms[roomIndex],
        ...updateData,
        updatedDate: new Date().toISOString().split('T')[0]
      };

      // Track room update
      StatusTracker.trackFloorRoomChange(
        'room',
        roomId,
        'updated',
        'owner', // In real app, this would be the actual user ID
        updateData
      );

      return {
        success: true,
        data: mockRooms[roomIndex],
        message: 'Room updated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to update room'
      };
    }
  }

  // Delete room
  static async deleteRoom(roomId: string): Promise<ApiResponse<void>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));

      const roomIndex = mockRooms.findIndex(room => room.id === roomId);
      if (roomIndex === -1) {
        return {
          success: false,
          error: 'Room not found'
        };
      }

      const room = mockRooms[roomIndex];

      // Check if room is occupied
      if (room.currentOccupancy > 0) {
        return {
          success: false,
          error: 'Cannot delete occupied room'
        };
      }

      mockRooms.splice(roomIndex, 1);

      // Update floor's total rooms count
      const floor = mockFloors.find(f => f.id === room.floorId);
      if (floor && floor.totalRooms > 0) {
        floor.totalRooms -= 1;
      }

      // Track room deletion
      StatusTracker.trackFloorRoomChange(
        'room',
        roomId,
        'deleted',
        'owner', // In real app, this would be the actual user ID
        { roomNumber: room.roomNumber }
      );

      return {
        success: true,
        message: 'Room deleted successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to delete room'
      };
    }
  }

  // Update room occupancy
  static async updateRoomOccupancy(
    roomId: string,
    occupancy: number
  ): Promise<ApiResponse<Room>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));

      const room = mockRooms.find(r => r.id === roomId);
      if (!room) {
        return {
          success: false,
          error: 'Room not found'
        };
      }

      if (occupancy < 0 || occupancy > room.capacity) {
        return {
          success: false,
          error: 'Invalid occupancy value'
        };
      }

      room.currentOccupancy = occupancy;
      room.status = occupancy === 0 ? 'available' : 'occupied';
      room.updatedDate = new Date().toISOString().split('T')[0];

      return {
        success: true,
        data: room,
        message: 'Room occupancy updated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to update room occupancy'
      };
    }
  }

  // Update room status
  static async updateRoomStatus(
    roomId: string,
    status: Room['status']
  ): Promise<ApiResponse<Room>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));

      const room = mockRooms.find(r => r.id === roomId);
      if (!room) {
        return {
          success: false,
          error: 'Room not found'
        };
      }

      room.status = status;
      room.updatedDate = new Date().toISOString().split('T')[0];

      return {
        success: true,
        data: room,
        message: 'Room status updated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to update room status'
      };
    }
  }
}

// Data validation utilities
export class ValidationService {
  // Validate hostel registration data
  static validateHostelRegistration(data: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!data.name || data.name.length < 3) {
      errors.push('Hostel name must be at least 3 characters long');
    }

    if (!data.address || data.address.length < 10) {
      errors.push('Address must be at least 10 characters long');
    }

    if (!data.city || data.city.length < 2) {
      errors.push('City is required');
    }

    if (!data.state || data.state.length < 2) {
      errors.push('State is required');
    }

    if (!data.pincode || !/^\d{6}$/.test(data.pincode)) {
      errors.push('Pincode must be 6 digits');
    }

    if (!data.totalBeds || data.totalBeds < 10 || data.totalBeds > 500) {
      errors.push('Total beds must be between 10 and 500');
    }

    if (!data.pricePerBed || data.pricePerBed < 1000 || data.pricePerBed > 50000) {
      errors.push('Price per bed must be between ₹1,000 and ₹50,000');
    }

    if (!data.contactPerson || data.contactPerson.length < 2) {
      errors.push('Contact person name is required');
    }

    if (!data.contactPhone || !/^\+91\s\d{10}$/.test(data.contactPhone)) {
      errors.push('Valid phone number is required');
    }

    if (!data.contactEmail || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.contactEmail)) {
      errors.push('Valid email address is required');
    }

    if (!data.licenseNumber || data.licenseNumber.length < 5) {
      errors.push('Business license number is required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Validate floor data
  static validateFloor(data: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!data.floorNumber || data.floorNumber < 0) {
      errors.push('Valid floor number is required');
    }

    if (!data.floorName || data.floorName.length < 2) {
      errors.push('Floor name is required');
    }

    if (!data.hostelId) {
      errors.push('Hostel ID is required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Validate room data
  static validateRoom(data: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!data.roomNumber || data.roomNumber.length < 1) {
      errors.push('Room number is required');
    }

    if (!data.roomType || !['single', 'double', 'triple', 'quad', 'dormitory'].includes(data.roomType)) {
      errors.push('Valid room type is required');
    }

    if (!data.capacity || data.capacity < 1 || data.capacity > 20) {
      errors.push('Room capacity must be between 1 and 20');
    }

    if (!data.pricePerBed || data.pricePerBed < 1000) {
      errors.push('Price per bed must be at least ₹1,000');
    }

    if (!data.floorId) {
      errors.push('Floor ID is required');
    }

    if (!data.hostelId) {
      errors.push('Hostel ID is required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Floor Management Service
export class FloorService {
  // Create new floor
  static async createFloor(
    floorData: Omit<Floor, 'id' | 'createdDate' | 'updatedDate'>
  ): Promise<ApiResponse<Floor>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));

      const newFloor: Floor = {
        ...floorData,
        id: `floor-${Date.now()}`,
        createdDate: new Date().toISOString().split('T')[0],
        updatedDate: new Date().toISOString().split('T')[0]
      };

      mockFloors.push(newFloor);

      // Track floor creation
      StatusTracker.trackFloorRoomChange(
        'floor',
        newFloor.id,
        'created',
        'owner', // In real app, this would be the actual user ID
        { floorName: newFloor.floorName, floorNumber: newFloor.floorNumber }
      );

      return {
        success: true,
        data: newFloor,
        message: 'Floor created successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to create floor'
      };
    }
  }

  // Get floors by hostel ID
  static async getFloorsByHostel(hostelId: string): Promise<ApiResponse<Floor[]>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));

      const floors = mockFloors.filter(floor => floor.hostelId === hostelId);
      
      return {
        success: true,
        data: floors
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to fetch floors'
      };
    }
  }

  // Update floor
  static async updateFloor(
    floorId: string,
    updateData: Partial<Floor>
  ): Promise<ApiResponse<Floor>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));

      const floorIndex = mockFloors.findIndex(floor => floor.id === floorId);
      if (floorIndex === -1) {
        return {
          success: false,
          error: 'Floor not found'
        };
      }

      mockFloors[floorIndex] = {
        ...mockFloors[floorIndex],
        ...updateData,
        updatedDate: new Date().toISOString().split('T')[0]
      };

      // Track floor update
      StatusTracker.trackFloorRoomChange(
        'floor',
        floorId,
        'updated',
        'owner', // In real app, this would be the actual user ID
        updateData
      );

      return {
        success: true,
        data: mockFloors[floorIndex],
        message: 'Floor updated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to update floor'
      };
    }
  }

  // Delete floor
  static async deleteFloor(floorId: string): Promise<ApiResponse<void>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));

      // Check if floor has rooms
      const floorRooms = mockRooms.filter(room => room.floorId === floorId);
      if (floorRooms.length > 0) {
        return {
          success: false,
          error: 'Cannot delete floor with existing rooms'
        };
      }

      const floorIndex = mockFloors.findIndex(floor => floor.id === floorId);
      if (floorIndex === -1) {
        return {
          success: false,
          error: 'Floor not found'
        };
      }

      const deletedFloor = mockFloors[floorIndex];
      mockFloors.splice(floorIndex, 1);

      // Track floor deletion
      StatusTracker.trackFloorRoomChange(
        'floor',
        floorId,
        'deleted',
        'owner', // In real app, this would be the actual user ID
        { floorName: deletedFloor.floorName }
      );

      return {
        success: true,
        message: 'Floor deleted successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to delete floor'
      };
    }
  }
}
