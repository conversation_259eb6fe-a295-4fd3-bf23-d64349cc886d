import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Building2,
  Users,
  CreditCard,
  AlertTriangle,
  TrendingUp,
  DollarSign,
  UserCheck,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  Eye,
  Settings,
  Bed
} from 'lucide-react';
import { mockHostels, mockUsers, mockPayments, mockComplaints } from '@/data/mockData';

export const SystemOverview: React.FC = () => {
  // Calculate system-wide statistics
  const totalHostels = mockHostels.length;
  const activeHostels = mockHostels.filter(h => h.status === 'active').length;
  const totalUsers = mockUsers.length;
  const activeUsers = mockUsers.filter(u => u.status === 'active').length;
  const totalRevenue = mockPayments.reduce((sum, p) => sum + p.amount, 0);
  const platformRevenue = mockPayments.reduce((sum, p) => sum + p.splitDetails.platform, 0);
  const openComplaints = mockComplaints.filter(c => c.status === 'open').length;
  const totalBeds = mockHostels.reduce((sum, h) => sum + h.totalBeds, 0);
  const occupiedBeds = mockHostels.reduce((sum, h) => sum + (h.totalBeds - h.availableBeds), 0);
  const occupancyRate = ((occupiedBeds / totalBeds) * 100).toFixed(1);

  const systemStats = [
    {
      title: 'Total Hostels',
      value: totalHostels,
      change: '+12%',
      trend: 'up',
      icon: Building2,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Active Users',
      value: activeUsers,
      change: '+8%',
      trend: 'up',
      icon: Users,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Platform Revenue',
      value: `₹${(platformRevenue / 1000).toFixed(0)}K`,
      change: '+15%',
      trend: 'up',
      icon: DollarSign,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'Occupancy Rate',
      value: `${occupancyRate}%`,
      change: '+3%',
      trend: 'up',
      icon: Activity,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      title: 'Open Complaints',
      value: openComplaints,
      change: '-5%',
      trend: 'down',
      icon: AlertTriangle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
    {
      title: 'Total Beds',
      value: totalBeds,
      change: '+7%',
      trend: 'up',
      icon: Bed,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
    },
  ];

  const recentActivities = [
    { action: 'New hostel registered', details: 'Urban Stay Delhi', time: '2 hours ago', type: 'hostel' },
    { action: 'User complaint resolved', details: 'Complaint #1234', time: '4 hours ago', type: 'complaint' },
    { action: 'Payment processed', details: '₹15,000 platform fee', time: '6 hours ago', type: 'payment' },
    { action: 'New employee added', details: 'Priya Sharma - Urban Stay', time: '1 day ago', type: 'user' },
    { action: 'Hostel status updated', details: 'Green Valley Hostel - Active', time: '2 days ago', type: 'hostel' },
  ];

  const topPerformingHostels = mockHostels
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 5);

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">System Overview</h1>
          <p className="text-muted-foreground">
            Monitor platform performance and key metrics
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Eye className="mr-2 h-4 w-4" />
            View Reports
          </Button>
          <Button size="sm">
            <Settings className="mr-2 h-4 w-4" />
            Platform Settings
          </Button>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {systemStats.map((stat, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {stat.trend === 'up' ? (
                  <ArrowUpRight className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <ArrowDownRight className="mr-1 h-3 w-3 text-red-500" />
                )}
                <span className={stat.trend === 'up' ? 'text-green-600' : 'text-red-600'}>
                  {stat.change}
                </span>
                <span className="ml-1">from last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Recent Activities */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activities</CardTitle>
            <CardDescription>
              Latest platform activities and updates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className={`p-1 rounded-full ${
                    activity.type === 'hostel' ? 'bg-blue-100' :
                    activity.type === 'complaint' ? 'bg-red-100' :
                    activity.type === 'payment' ? 'bg-green-100' :
                    'bg-gray-100'
                  }`}>
                    <div className="w-2 h-2 rounded-full bg-current opacity-60" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">
                      {activity.action}
                    </p>
                    <p className="text-sm text-gray-500">{activity.details}</p>
                    <p className="text-xs text-gray-400">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Top Performing Hostels */}
        <Card>
          <CardHeader>
            <CardTitle>Top Performing Hostels</CardTitle>
            <CardDescription>
              Highest rated hostels on the platform
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topPerformingHostels.map((hostel, index) => (
                <div key={hostel.id} className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center text-white text-sm font-medium">
                      {index + 1}
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {hostel.name}
                    </p>
                    <p className="text-sm text-gray-500">{hostel.city}, {hostel.state}</p>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className="text-sm font-medium">{hostel.rating}</span>
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <span
                          key={i}
                          className={`text-xs ${
                            i < Math.floor(hostel.rating) ? 'text-yellow-400' : 'text-gray-300'
                          }`}
                        >
                          ★
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common administrative tasks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            <Button variant="outline" className="h-20 flex-col">
              <Building2 className="h-6 w-6 mb-2" />
              <span className="text-sm">Add Hostel</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Users className="h-6 w-6 mb-2" />
              <span className="text-sm">Manage Users</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <AlertTriangle className="h-6 w-6 mb-2" />
              <span className="text-sm">View Complaints</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <CreditCard className="h-6 w-6 mb-2" />
              <span className="text-sm">Payment Reports</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
