/* Drag and Drop Animation Styles */

/* Dragging animations */
.drag-item {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.drag-item:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.drag-item.dragging {
  transform: scale(0.95) rotate(2deg);
  opacity: 0.8;
  z-index: 1000;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
}

.drag-item.drag-preview {
  transform: scale(0.9) rotate(5deg);
  opacity: 0.9;
  pointer-events: none;
  z-index: 1001;
}

/* Drop zone animations */
.drop-zone {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.drop-zone::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
  pointer-events: none;
}

.drop-zone.drag-over {
  transform: scale(1.02);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5), 0 4px 12px rgba(59, 130, 246, 0.2);
}

.drop-zone.drag-over::before {
  transform: translateX(100%);
}

.drop-zone.valid-drop {
  border-color: #10b981;
  background-color: rgba(16, 185, 129, 0.05);
}

.drop-zone.invalid-drop {
  border-color: #ef4444;
  background-color: rgba(239, 68, 68, 0.05);
}

/* Compatibility indicators */
.compatibility-indicator {
  animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.4);
  }
}

.compatibility-excellent {
  animation: success-pulse 1.5s infinite;
}

@keyframes success-pulse {
  0%, 100% {
    box-shadow: 0 0 5px rgba(16, 185, 129, 0.5);
  }
  50% {
    box-shadow: 0 0 15px rgba(16, 185, 129, 0.8);
  }
}

.compatibility-poor {
  animation: warning-pulse 1.5s infinite;
}

@keyframes warning-pulse {
  0%, 100% {
    box-shadow: 0 0 5px rgba(239, 68, 68, 0.5);
  }
  50% {
    box-shadow: 0 0 15px rgba(239, 68, 68, 0.8);
  }
}

/* Tenant card animations */
.tenant-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tenant-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.tenant-card.allocated {
  opacity: 0.7;
  transform: scale(0.98);
}

.tenant-card.dragging {
  transform: scale(0.95) rotate(1deg);
  opacity: 0.8;
  z-index: 1000;
}

/* Bed zone animations */
.bed-zone {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.bed-zone:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.bed-zone.available {
  cursor: pointer;
}

.bed-zone.occupied {
  opacity: 0.9;
}

.bed-zone.maintenance {
  opacity: 0.8;
  filter: grayscale(20%);
}

/* Drop feedback animations */
.drop-feedback {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: drop-feedback-appear 0.3s ease-out;
  z-index: 10;
}

@keyframes drop-feedback-appear {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.drop-feedback.success {
  color: #059669;
  border: 1px solid #10b981;
}

.drop-feedback.error {
  color: #dc2626;
  border: 1px solid #ef4444;
}

/* Floor plan animations */
.floor-section {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.floor-section.expanded {
  transform: scale(1.01);
}

.floor-section.collapsed {
  transform: scale(0.99);
  opacity: 0.8;
}

/* Room layout animations */
.room-layout {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.room-layout:hover {
  transform: scale(1.01);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Allocation success animation */
.allocation-success {
  animation: allocation-success-bounce 0.6s ease-out;
}

@keyframes allocation-success-bounce {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.6);
  }
  100% {
    transform: scale(1);
  }
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Drag ghost image */
.drag-ghost {
  opacity: 0.5;
  transform: rotate(5deg) scale(0.9);
  pointer-events: none;
}

/* Responsive animations */
@media (max-width: 768px) {
  .drag-item:hover {
    transform: none;
  }
  
  .drop-zone.drag-over {
    transform: none;
  }
  
  .tenant-card:hover {
    transform: none;
  }
  
  .bed-zone:hover {
    transform: none;
  }
}

/* Accessibility - Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .drag-item,
  .drop-zone,
  .tenant-card,
  .bed-zone,
  .floor-section,
  .room-layout {
    transition: none;
    animation: none;
  }
  
  .drag-item:hover,
  .drop-zone.drag-over,
  .tenant-card:hover,
  .bed-zone:hover {
    transform: none;
  }
  
  .compatibility-indicator,
  .compatibility-excellent,
  .compatibility-poor {
    animation: none;
  }
}
