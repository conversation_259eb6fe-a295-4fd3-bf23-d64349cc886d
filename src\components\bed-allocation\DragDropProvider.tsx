// Drag and Drop Provider for Bed Allocation System
// Note: In a real implementation, you would install @dnd-kit/core, @dnd-kit/sortable, and @dnd-kit/utilities
// For this implementation, I'll create a custom drag-and-drop system using React's built-in events

import React, { createContext, useContext, useState, useCallback, ReactNode, useEffect } from 'react';
import { Bed, HostelTenant } from '@/data/mockData';
import './drag-drop-animations.css';

// Drag and Drop Context Types
interface DragDropContextType {
  draggedItem: DraggedItem | null;
  dragOverTarget: string | null;
  isDragging: boolean;
  startDrag: (item: DraggedItem) => void;
  endDrag: () => void;
  setDragOverTarget: (target: string | null) => void;
  handleDrop: (targetBedId: string, tenant: HostelTenant) => void;
  onDropCallback?: (bedId: string, tenant: HostelTenant) => void;
  setOnDropCallback: (callback: (bedId: string, tenant: HostelTenant) => void) => void;
}

interface DraggedItem {
  type: 'tenant';
  data: HostelTenant;
}

interface DragDropProviderProps {
  children: ReactNode;
}

// Create Context
const DragDropContext = createContext<DragDropContextType | null>(null);

// Custom Hook to use Drag Drop Context
export const useDragDrop = () => {
  const context = useContext(DragDropContext);
  if (!context) {
    throw new Error('useDragDrop must be used within a DragDropProvider');
  }
  return context;
};

// Drag Drop Provider Component
export const DragDropProvider: React.FC<DragDropProviderProps> = ({ children }) => {
  const [draggedItem, setDraggedItem] = useState<DraggedItem | null>(null);
  const [dragOverTarget, setDragOverTarget] = useState<string | null>(null);
  const [onDropCallback, setOnDropCallback] = useState<((bedId: string, tenant: HostelTenant) => void) | undefined>();

  const isDragging = draggedItem !== null;

  const startDrag = useCallback((item: DraggedItem) => {
    setDraggedItem(item);
  }, []);

  const endDrag = useCallback(() => {
    setDraggedItem(null);
    setDragOverTarget(null);
  }, []);

  const handleDrop = useCallback((targetBedId: string, tenant: HostelTenant) => {
    if (onDropCallback) {
      onDropCallback(targetBedId, tenant);
    }
    endDrag();
  }, [onDropCallback, endDrag]);

  const contextValue: DragDropContextType = {
    draggedItem,
    dragOverTarget,
    isDragging,
    startDrag,
    endDrag,
    setDragOverTarget,
    handleDrop,
    onDropCallback,
    setOnDropCallback
  };

  return (
    <DragDropContext.Provider value={contextValue}>
      {children}
    </DragDropContext.Provider>
  );
};

// Draggable Component Wrapper
interface DraggableProps {
  children: ReactNode;
  item: DraggedItem;
  className?: string;
  disabled?: boolean;
}

export const Draggable: React.FC<DraggableProps> = ({ 
  children, 
  item, 
  className = '', 
  disabled = false 
}) => {
  const { startDrag, endDrag, isDragging, draggedItem } = useDragDrop();
  const isCurrentlyDragged = draggedItem?.data.id === item.data.id;

  const handleDragStart = (e: React.DragEvent) => {
    if (disabled) return;
    
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', ''); // Required for Firefox
    startDrag(item);
  };

  const handleDragEnd = () => {
    if (disabled) return;
    endDrag();
  };

  return (
    <div
      draggable={!disabled}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      className={`
        drag-item
        ${className}
        ${!disabled ? 'cursor-grab active:cursor-grabbing' : 'cursor-not-allowed opacity-50'}
        ${isCurrentlyDragged ? 'dragging' : ''}
        ${isDragging && !isCurrentlyDragged ? 'pointer-events-none opacity-60' : ''}
      `}
      style={{
        userSelect: 'none',
        WebkitUserSelect: 'none',
        MozUserSelect: 'none',
        msUserSelect: 'none'
      }}
    >
      {children}

      {/* Visual drag indicator */}
      {!disabled && (
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
        </div>
      )}
    </div>
  );
};

// Droppable Component Wrapper
interface DroppableProps {
  children: ReactNode;
  id: string;
  className?: string;
  disabled?: boolean;
  onDrop?: (item: DraggedItem) => void;
  isValidDrop?: (item: DraggedItem) => boolean;
}

export const Droppable: React.FC<DroppableProps> = ({ 
  children, 
  id, 
  className = '', 
  disabled = false,
  onDrop,
  isValidDrop
}) => {
  const { 
    draggedItem, 
    dragOverTarget, 
    setDragOverTarget, 
    handleDrop, 
    isDragging 
  } = useDragDrop();

  const isValidDropZone = draggedItem && isValidDrop ? isValidDrop(draggedItem) : true;
  const isOver = dragOverTarget === id;
  const canDrop = isDragging && !disabled && isValidDropZone;

  const handleDragOver = (e: React.DragEvent) => {
    if (disabled || !isValidDropZone) return;
    
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverTarget(id);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    // Only clear drag over if we're actually leaving this element
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setDragOverTarget(null);
    }
  };

  const handleDropEvent = (e: React.DragEvent) => {
    e.preventDefault();
    
    if (disabled || !draggedItem || !isValidDropZone) return;

    if (draggedItem.type === 'tenant') {
      handleDrop(id, draggedItem.data);
    }

    if (onDrop) {
      onDrop(draggedItem);
    }
  };

  return (
    <div
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDropEvent}
      className={`
        drop-zone
        ${className}
        ${canDrop && isOver ? 'drag-over valid-drop' : ''}
        ${isDragging && !isValidDropZone ? 'invalid-drop opacity-50 cursor-not-allowed' : ''}
        ${canDrop && !isOver && isDragging ? 'ring-1 ring-gray-300 ring-opacity-30' : ''}
      `}
    >
      {children}

      {/* Drop feedback indicator */}
      {isDragging && (
        <div className={`
          absolute inset-0 pointer-events-none rounded-lg border-2 border-dashed transition-all duration-300
          ${isValidDropZone ? 'border-green-400 bg-green-50 bg-opacity-20' : 'border-red-400 bg-red-50 bg-opacity-20'}
          ${isOver ? 'opacity-100' : 'opacity-0'}
        `}>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className={`
              px-3 py-1 rounded-full text-xs font-medium
              ${isValidDropZone ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}
            `}>
              {isValidDropZone ? 'Drop here' : 'Not compatible'}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Utility function to check if a tenant is compatible with a bed
export const isTenantCompatibleWithBed = (tenant: HostelTenant, bed: Bed): boolean => {
  // Check if bed is available
  if (bed.status !== 'available') {
    return false;
  }

  // Check budget compatibility
  if (bed.pricePerMonth > tenant.budgetRange.max || bed.pricePerMonth < tenant.budgetRange.min) {
    return false;
  }

  // Check room type preference (if specified)
  if (tenant.preferredRoomType) {
    // This would need to be checked against the room type
    // For now, we'll assume compatibility
  }

  return true;
};

// Utility function to get compatibility score
export const getTenantBedCompatibilityScore = (tenant: HostelTenant, bed: Bed): number => {
  if (!isTenantCompatibleWithBed(tenant, bed)) {
    return 0;
  }

  let score = 50; // Base compatibility score

  // Budget match score (closer to preferred range = higher score)
  const budgetMid = (tenant.budgetRange.min + tenant.budgetRange.max) / 2;
  const budgetDiff = Math.abs(bed.pricePerMonth - budgetMid);
  const maxBudgetRange = tenant.budgetRange.max - tenant.budgetRange.min;
  const budgetScore = Math.max(0, 50 - (budgetDiff / maxBudgetRange) * 50);
  
  score += budgetScore;

  // Amenities match score
  let amenityScore = 0;
  if (bed.hasStorage) amenityScore += 5;
  if (bed.hasPrivacyCurtain) amenityScore += 5;
  if (bed.hasReadingLight) amenityScore += 5;
  if (bed.hasPowerOutlet) amenityScore += 5;
  
  score += amenityScore;

  return Math.min(100, Math.round(score));
};

// Visual feedback component for drag operations
interface DragOverlayProps {
  children: ReactNode;
}

export const DragOverlay: React.FC<DragOverlayProps> = ({ children }) => {
  const { isDragging, draggedItem } = useDragDrop();

  if (!isDragging || !draggedItem) {
    return null;
  }

  return (
    <div className="fixed inset-0 pointer-events-none z-50">
      <div className="absolute top-0 left-0 transform -translate-x-1/2 -translate-y-1/2 opacity-80 scale-95">
        {children}
      </div>
    </div>
  );
};
