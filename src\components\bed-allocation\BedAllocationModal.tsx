import React, { useState } from 'react';
import { 
  User, 
  Calendar, 
  CreditCard, 
  FileText, 
  Search,
  UserPlus,
  X
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { 
  Bed, 
  HostelTenant, 
  BedAllocation,
  mockHostelTenants,
  mockBeds,
  mockBedAllocations
} from '@/data/mockData';

interface BedAllocationModalProps {
  bed: Bed;
  isOpen: boolean;
  onClose: () => void;
  onAllocate: (allocation: Partial<BedAllocation>) => void;
}

export const BedAllocationModal: React.FC<BedAllocationModalProps> = ({
  bed,
  isOpen,
  onClose,
  onAllocate
}) => {
  const { toast } = useToast();
  const [step, setStep] = useState<'select-tenant' | 'allocation-details'>('select-tenant');
  const [selectedTenant, setSelectedTenant] = useState<HostelTenant | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [allocationData, setAllocationData] = useState({
    checkInDate: new Date().toISOString().split('T')[0],
    expectedCheckOutDate: '',
    monthlyRent: bed.pricePerMonth,
    securityDeposit: bed.pricePerMonth * 2,
    advancePayment: bed.pricePerMonth,
    allocationNotes: ''
  });

  // Filter available tenants (not currently allocated to any bed)
  const availableTenants = mockHostelTenants.filter(tenant => 
    tenant.status === 'active' && 
    !tenant.currentBedId &&
    (tenant.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
     tenant.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
     tenant.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
     tenant.phone.includes(searchTerm))
  );

  const handleTenantSelect = (tenant: HostelTenant) => {
    setSelectedTenant(tenant);
    setStep('allocation-details');
  };

  const handleAllocate = () => {
    if (!selectedTenant) return;

    const allocation: Partial<BedAllocation> = {
      bedId: bed.id,
      roomId: bed.roomId,
      hostelId: bed.hostelId,
      tenantId: selectedTenant.id,
      allocationDate: new Date().toISOString().split('T')[0],
      checkInDate: allocationData.checkInDate,
      expectedCheckOutDate: allocationData.expectedCheckOutDate,
      monthlyRent: allocationData.monthlyRent,
      securityDeposit: allocationData.securityDeposit,
      advancePayment: allocationData.advancePayment,
      status: 'active',
      allocationNotes: allocationData.allocationNotes,
      allocatedBy: '2', // Current user ID
      createdDate: new Date().toISOString().split('T')[0],
      updatedDate: new Date().toISOString().split('T')[0]
    };

    onAllocate(allocation);
    
    toast({
      title: 'Bed Allocated Successfully',
      description: `Bed ${bed.bedNumber} has been allocated to ${selectedTenant.firstName} ${selectedTenant.lastName}`,
    });

    handleClose();
  };

  const handleClose = () => {
    setStep('select-tenant');
    setSelectedTenant(null);
    setSearchTerm('');
    setAllocationData({
      checkInDate: new Date().toISOString().split('T')[0],
      expectedCheckOutDate: '',
      monthlyRent: bed.pricePerMonth,
      securityDeposit: bed.pricePerMonth * 2,
      advancePayment: bed.pricePerMonth,
      allocationNotes: ''
    });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Allocate Bed {bed.bedNumber}
          </DialogTitle>
          <DialogDescription>
            Assign this bed to a tenant and set up the allocation details
          </DialogDescription>
        </DialogHeader>

        {step === 'select-tenant' && (
          <div className="space-y-6">
            {/* Search Bar */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search tenants by name, email, or phone..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Available Tenants */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Available Tenants</h3>
              
              {availableTenants.length === 0 ? (
                <Card>
                  <CardContent className="text-center py-8">
                    <User className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h4 className="text-lg font-semibold mb-2">No Available Tenants</h4>
                    <p className="text-muted-foreground mb-4">
                      {searchTerm ? 'No tenants match your search criteria' : 'All active tenants are currently allocated to beds'}
                    </p>
                    <Button variant="outline" onClick={() => setSearchTerm('')}>
                      Clear Search
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid gap-4 md:grid-cols-2">
                  {availableTenants.map((tenant) => (
                    <Card 
                      key={tenant.id} 
                      className="cursor-pointer hover:shadow-md transition-shadow border-2 hover:border-primary"
                      onClick={() => handleTenantSelect(tenant)}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg">
                            {tenant.firstName} {tenant.lastName}
                          </CardTitle>
                          <Badge variant="outline" className="capitalize">
                            {tenant.occupation.replace('_', ' ')}
                          </Badge>
                        </div>
                        <CardDescription>{tenant.email}</CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Phone:</span>
                          <span>{tenant.phone}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Budget Range:</span>
                          <span>₹{tenant.budgetRange.min.toLocaleString()} - ₹{tenant.budgetRange.max.toLocaleString()}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Preferred Room:</span>
                          <span className="capitalize">{tenant.preferredRoomType || 'Any'}</span>
                        </div>
                        {tenant.company && (
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-muted-foreground">Company:</span>
                            <span className="text-right">{tenant.company}</span>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-3">
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>
            </div>
          </div>
        )}

        {step === 'allocation-details' && selectedTenant && (
          <div className="space-y-6">
            {/* Selected Tenant Info */}
            <Card className="border-primary">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Selected Tenant
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Name</p>
                    <p className="font-medium">{selectedTenant.firstName} {selectedTenant.lastName}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Phone</p>
                    <p>{selectedTenant.phone}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Email</p>
                    <p className="text-sm">{selectedTenant.email}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Occupation</p>
                    <p className="capitalize">{selectedTenant.occupation.replace('_', ' ')}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Allocation Details Form */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Allocation Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="checkInDate">Check-in Date</Label>
                    <Input
                      id="checkInDate"
                      type="date"
                      value={allocationData.checkInDate}
                      onChange={(e) => setAllocationData(prev => ({
                        ...prev,
                        checkInDate: e.target.value
                      }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="expectedCheckOutDate">Expected Check-out Date</Label>
                    <Input
                      id="expectedCheckOutDate"
                      type="date"
                      value={allocationData.expectedCheckOutDate}
                      onChange={(e) => setAllocationData(prev => ({
                        ...prev,
                        expectedCheckOutDate: e.target.value
                      }))}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="monthlyRent">Monthly Rent (₹)</Label>
                    <Input
                      id="monthlyRent"
                      type="number"
                      value={allocationData.monthlyRent}
                      onChange={(e) => setAllocationData(prev => ({
                        ...prev,
                        monthlyRent: parseInt(e.target.value) || 0
                      }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="securityDeposit">Security Deposit (₹)</Label>
                    <Input
                      id="securityDeposit"
                      type="number"
                      value={allocationData.securityDeposit}
                      onChange={(e) => setAllocationData(prev => ({
                        ...prev,
                        securityDeposit: parseInt(e.target.value) || 0
                      }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="advancePayment">Advance Payment (₹)</Label>
                    <Input
                      id="advancePayment"
                      type="number"
                      value={allocationData.advancePayment}
                      onChange={(e) => setAllocationData(prev => ({
                        ...prev,
                        advancePayment: parseInt(e.target.value) || 0
                      }))}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="allocationNotes">Allocation Notes (Optional)</Label>
                  <Textarea
                    id="allocationNotes"
                    placeholder="Add any special notes or conditions for this allocation..."
                    value={allocationData.allocationNotes}
                    onChange={(e) => setAllocationData(prev => ({
                      ...prev,
                      allocationNotes: e.target.value
                    }))}
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Summary */}
            <Card className="bg-muted/50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Payment Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex justify-between">
                    <span>Monthly Rent:</span>
                    <span className="font-medium">₹{allocationData.monthlyRent.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Security Deposit:</span>
                    <span className="font-medium">₹{allocationData.securityDeposit.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Advance Payment:</span>
                    <span className="font-medium">₹{allocationData.advancePayment.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between font-semibold text-base pt-2 border-t">
                    <span>Total Initial Payment:</span>
                    <span>₹{(allocationData.securityDeposit + allocationData.advancePayment).toLocaleString()}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="flex justify-between">
              <Button variant="outline" onClick={() => setStep('select-tenant')}>
                <X className="h-4 w-4 mr-2" />
                Back to Tenant Selection
              </Button>
              <div className="flex gap-3">
                <Button variant="outline" onClick={handleClose}>
                  Cancel
                </Button>
                <Button onClick={handleAllocate}>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Allocate Bed
                </Button>
              </div>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
