import React, { useState } from 'react';
import { 
  UserMinus, 
  Calendar, 
  CreditCard, 
  FileText, 
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { 
  Bed, 
  HostelTenant, 
  BedAllocation,
  getAllocationByBedId,
  getHostelTenantById
} from '@/data/mockData';

interface CheckOutModalProps {
  bed: Bed;
  isOpen: boolean;
  onClose: () => void;
  onCheckOut: (checkOutData: {
    actualCheckOutDate: string;
    terminationReason?: string;
    refundAmount: number;
    deductionAmount: number;
    deductionReason?: string;
    checkOutNotes?: string;
  }) => void;
}

export const CheckOutModal: React.FC<CheckOutModalProps> = ({
  bed,
  isOpen,
  onClose,
  onCheckOut
}) => {
  const { toast } = useToast();
  const allocation = getAllocationByBedId(bed.id);
  const tenant = bed.currentTenantId ? getHostelTenantById(bed.currentTenantId) : null;

  const [checkOutData, setCheckOutData] = useState({
    actualCheckOutDate: new Date().toISOString().split('T')[0],
    terminationReason: '',
    refundAmount: allocation?.securityDeposit || 0,
    deductionAmount: 0,
    deductionReason: '',
    checkOutNotes: ''
  });

  const handleCheckOut = () => {
    if (!allocation || !tenant) return;

    onCheckOut(checkOutData);
    
    toast({
      title: 'Check-out Completed',
      description: `${tenant.firstName} ${tenant.lastName} has been checked out from bed ${bed.bedNumber}`,
    });

    onClose();
  };

  const calculateRefund = () => {
    const securityDeposit = allocation?.securityDeposit || 0;
    return Math.max(0, securityDeposit - checkOutData.deductionAmount);
  };

  const calculateStayDuration = () => {
    if (!allocation) return 0;
    const checkInDate = new Date(allocation.checkInDate);
    const checkOutDate = new Date(checkOutData.actualCheckOutDate);
    const diffTime = Math.abs(checkOutDate.getTime() - checkInDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  if (!allocation || !tenant) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Error</DialogTitle>
            <DialogDescription>
              Unable to find allocation or tenant information for this bed.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end">
            <Button onClick={onClose}>Close</Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserMinus className="h-5 w-5" />
            Check Out from Bed {bed.bedNumber}
          </DialogTitle>
          <DialogDescription>
            Process the check-out for {tenant.firstName} {tenant.lastName}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current Allocation Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Current Allocation Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Tenant</p>
                  <p className="font-medium">{tenant.firstName} {tenant.lastName}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Check-in Date</p>
                  <p>{new Date(allocation.checkInDate).toLocaleDateString()}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Expected Check-out</p>
                  <p>{new Date(allocation.expectedCheckOutDate).toLocaleDateString()}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Stay Duration</p>
                  <p>{calculateStayDuration()} days</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Monthly Rent</p>
                  <p className="font-medium">₹{allocation.monthlyRent.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Security Deposit</p>
                  <p className="font-medium">₹{allocation.securityDeposit.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Check-out Details Form */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Check-out Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="actualCheckOutDate">Actual Check-out Date</Label>
                  <Input
                    id="actualCheckOutDate"
                    type="date"
                    value={checkOutData.actualCheckOutDate}
                    onChange={(e) => setCheckOutData(prev => ({
                      ...prev,
                      actualCheckOutDate: e.target.value
                    }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="terminationReason">Termination Reason</Label>
                  <Select 
                    value={checkOutData.terminationReason} 
                    onValueChange={(value) => setCheckOutData(prev => ({
                      ...prev,
                      terminationReason: value
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select reason" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="normal_completion">Normal Completion</SelectItem>
                      <SelectItem value="early_termination_tenant">Early Termination by Tenant</SelectItem>
                      <SelectItem value="early_termination_owner">Early Termination by Owner</SelectItem>
                      <SelectItem value="violation_of_rules">Violation of Rules</SelectItem>
                      <SelectItem value="non_payment">Non-payment of Rent</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="checkOutNotes">Check-out Notes</Label>
                <Textarea
                  id="checkOutNotes"
                  placeholder="Add any notes about the check-out process, room condition, etc."
                  value={checkOutData.checkOutNotes}
                  onChange={(e) => setCheckOutData(prev => ({
                    ...prev,
                    checkOutNotes: e.target.value
                  }))}
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Security Deposit Refund */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Security Deposit Refund
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="deductionAmount">Deduction Amount (₹)</Label>
                  <Input
                    id="deductionAmount"
                    type="number"
                    min="0"
                    max={allocation.securityDeposit}
                    value={checkOutData.deductionAmount}
                    onChange={(e) => setCheckOutData(prev => ({
                      ...prev,
                      deductionAmount: Math.min(
                        parseInt(e.target.value) || 0, 
                        allocation?.securityDeposit || 0
                      )
                    }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Refund Amount (₹)</Label>
                  <div className="p-2 bg-muted rounded-md">
                    <span className="font-medium text-green-600">
                      ₹{calculateRefund().toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>

              {checkOutData.deductionAmount > 0 && (
                <div className="space-y-2">
                  <Label htmlFor="deductionReason">Reason for Deduction</Label>
                  <Textarea
                    id="deductionReason"
                    placeholder="Explain why amount is being deducted from security deposit..."
                    value={checkOutData.deductionReason}
                    onChange={(e) => setCheckOutData(prev => ({
                      ...prev,
                      deductionReason: e.target.value
                    }))}
                    rows={2}
                  />
                </div>
              )}

              <div className="bg-muted/50 p-4 rounded-lg">
                <h4 className="font-medium mb-2">Refund Summary</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Original Security Deposit:</span>
                    <span>₹{allocation.securityDeposit.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Deduction:</span>
                    <span className="text-red-600">-₹{checkOutData.deductionAmount.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between font-medium text-base pt-2 border-t">
                    <span>Final Refund Amount:</span>
                    <span className="text-green-600">₹{calculateRefund().toLocaleString()}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Warnings */}
          {new Date(checkOutData.actualCheckOutDate) < new Date(allocation.expectedCheckOutDate) && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                This is an early check-out. The expected check-out date was {new Date(allocation.expectedCheckOutDate).toLocaleDateString()}.
              </AlertDescription>
            </Alert>
          )}

          {checkOutData.deductionAmount > 0 && !checkOutData.deductionReason.trim() && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Please provide a reason for the security deposit deduction.
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleCheckOut}
              disabled={checkOutData.deductionAmount > 0 && !checkOutData.deductionReason.trim()}
            >
              <UserMinus className="h-4 w-4 mr-2" />
              Complete Check-out
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
