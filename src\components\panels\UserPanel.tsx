import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Search, 
  MapPin, 
  Star, 
  Wifi, 
  Car, 
  Utensils,
  Shield,
  Bed,
  CreditCard,
  MessageSquare,
  Eye
} from 'lucide-react';
import { mockHostels, mockBookings, getUserById, getBookingsByUserId } from '@/data/mockData';

export const UserPanel = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCity, setSelectedCity] = useState('');
  
  // Assuming current user is member with ID '11' (Rohit Verma)
  const currentUserId = '11';
  const currentUser = getUserById(currentUserId);
  const userBookings = getBookingsByUserId(currentUserId);
  const currentBooking = userBookings[0]; // Assuming user has current booking
  const currentHostel = mockHostels.find(h => h.id === currentBooking?.hostelId);

  // Filter hostels based on search
  const filteredHostels = mockHostels.filter(hostel => 
    hostel.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    hostel.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
    hostel.address.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getAmenityIcon = (amenity: string) => {
    const icons: { [key: string]: any } = {
      'WiFi': Wifi,
      'AC': Star,
      'Parking': Car,
      'Mess': Utensils,
      'Security': Shield,
      'Gym': Star,
      'Laundry': Star,
      'Study Room': Star,
      'Swimming Pool': Star,
      'Cafeteria': Utensils
    };
    return icons[amenity] || Star;
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Find Your Perfect Hostel</h1>
          <p className="text-muted-foreground">Discover and book the best hostels near you</p>
        </div>
        {currentBooking && (
          <Button variant="outline" className="flex items-center gap-2">
            <Bed className="h-4 w-4" />
            My Booking: {currentHostel?.name}
          </Button>
        )}
      </div>

      {/* Search Bar */}
      <Card>
        <CardContent className="p-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search hostels by name, city, or location..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Button className="bg-gradient-primary text-primary-foreground shadow-elevation">
              Search
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Current Booking (if exists) */}
      {currentBooking && currentHostel && (
        <Card className="border-primary/20 bg-gradient-to-r from-primary/5 to-transparent">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bed className="h-5 w-5 text-primary" />
              Your Current Accommodation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-xl font-semibold mb-2">{currentHostel.name}</h3>
                <p className="text-muted-foreground mb-2">
                  <MapPin className="inline h-4 w-4 mr-1" />
                  {currentHostel.address}, {currentHostel.city}
                </p>
                <div className="flex items-center gap-2 mb-4">
                  <Star className="h-4 w-4 text-yellow-500 fill-current" />
                  <span>{currentHostel.rating}</span>
                  <Badge>Bed: {currentBooking.bedNumber}</Badge>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Check-in:</span>
                  <span className="font-medium">{currentBooking.checkIn}</span>
                </div>
                <div className="flex justify-between">
                  <span>Check-out:</span>
                  <span className="font-medium">{currentBooking.checkOut}</span>
                </div>
                <div className="flex justify-between">
                  <span>Monthly Fee:</span>
                  <span className="font-medium">₹{currentHostel.pricePerBed}</span>
                </div>
                <div className="flex gap-2 mt-4">
                  <Button size="sm" variant="outline" className="flex-1">
                    <MessageSquare className="h-4 w-4 mr-1" />
                    Contact
                  </Button>
                  <Button size="sm" variant="outline" className="flex-1">
                    <CreditCard className="h-4 w-4 mr-1" />
                    Pay Dues
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Available Hostels */}
      <div>
        <h2 className="text-2xl font-bold mb-4">Available Hostels</h2>
        <div className="grid gap-6">
          {filteredHostels.map((hostel) => (
            <Card key={hostel.id} className="hover:shadow-elevation transition-all duration-300">
              <CardContent className="p-6">
                <div className="grid md:grid-cols-3 gap-6">
                  {/* Hostel Image */}
                  <div className="md:col-span-1">
                    <div className="w-full h-48 bg-muted rounded-lg flex items-center justify-center">
                      <Building2 className="h-16 w-16 text-muted-foreground" />
                    </div>
                  </div>

                  {/* Hostel Details */}
                  <div className="md:col-span-2">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-xl font-semibold">{hostel.name}</h3>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-primary">₹{hostel.pricePerBed}</div>
                        <div className="text-sm text-muted-foreground">per month</div>
                      </div>
                    </div>

                    <p className="text-muted-foreground mb-2">
                      <MapPin className="inline h-4 w-4 mr-1" />
                      {hostel.address}, {hostel.city}, {hostel.state}
                    </p>

                    <div className="flex items-center gap-4 mb-4">
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 text-yellow-500 fill-current" />
                        <span>{hostel.rating}</span>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {hostel.availableBeds} of {hostel.totalBeds} beds available
                      </div>
                      <Badge variant={hostel.availableBeds > 0 ? 'default' : 'secondary'}>
                        {hostel.availableBeds > 0 ? 'Available' : 'Full'}
                      </Badge>
                    </div>

                    {/* Amenities */}
                    <div className="mb-4">
                      <div className="text-sm font-medium mb-2">Amenities:</div>
                      <div className="flex flex-wrap gap-2">
                        {hostel.amenities.map((amenity) => {
                          const Icon = getAmenityIcon(amenity);
                          return (
                            <div key={amenity} className="flex items-center gap-1 text-sm bg-muted px-2 py-1 rounded">
                              <Icon className="h-3 w-3" />
                              {amenity}
                            </div>
                          );
                        })}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        className="flex-1"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View Details
                      </Button>
                      <Button 
                        size="sm"
                        className="flex-1 bg-gradient-primary text-primary-foreground"
                        disabled={hostel.availableBeds === 0}
                      >
                        <Bed className="h-4 w-4 mr-1" />
                        {hostel.availableBeds > 0 ? 'Book Now' : 'Fully Booked'}
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

// Building2 icon import
import { Building2 } from 'lucide-react';