// Custom hook for real-time bed allocation updates

import { useState, useEffect, useCallback } from 'react';
import { 
  Bed, 
  BedAllocation, 
  HostelTenant,
  mockBeds,
  mockBedAllocations,
  mockHostelTenants,
  getBedsByHostelId,
  getAllocationsByHostelId,
  getActiveAllocationsByHostelId,
  getHostelOccupancySummary
} from '@/data/mockData';
import { BedAllocationService } from '@/services/bedAllocationService';
import { NotificationService } from '@/utils/notificationService';

interface BedAllocationUpdates {
  beds: Bed[];
  allocations: BedAllocation[];
  occupancySummary: ReturnType<typeof getHostelOccupancySummary>;
  upcomingEvents: Array<{
    type: 'check-in' | 'check-out';
    date: string;
    allocation: BedAllocation;
    tenant: HostelTenant;
    bed: Bed;
  }>;
  isLoading: boolean;
  error: string | null;
  refreshData: () => Promise<void>;
}

export const useBedAllocationUpdates = (hostelId: string): BedAllocationUpdates => {
  const [beds, setBeds] = useState<Bed[]>([]);
  const [allocations, setAllocations] = useState<BedAllocation[]>([]);
  const [occupancySummary, setOccupancySummary] = useState<ReturnType<typeof getHostelOccupancySummary>>(
    getHostelOccupancySummary('')
  );
  const [upcomingEvents, setUpcomingEvents] = useState<Array<{
    type: 'check-in' | 'check-out';
    date: string;
    allocation: BedAllocation;
    tenant: HostelTenant;
    bed: Bed;
  }>>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const refreshData = useCallback(async () => {
    if (!hostelId) return;

    setIsLoading(true);
    setError(null);

    try {
      // Fetch beds data
      const bedsData = getBedsByHostelId(hostelId);
      setBeds(bedsData);

      // Fetch allocations data
      const allocationsResult = await BedAllocationService.getBedAllocationHistory(hostelId);
      if (allocationsResult.success && allocationsResult.data) {
        setAllocations(allocationsResult.data);
      }

      // Fetch occupancy summary
      const occupancyResult = await BedAllocationService.getBedOccupancySummary(hostelId);
      if (occupancyResult.success && occupancyResult.data) {
        setOccupancySummary(occupancyResult.data);
      }

      // Fetch upcoming events
      const eventsResult = await BedAllocationService.getUpcomingEvents(hostelId, 7);
      if (eventsResult.success && eventsResult.data) {
        setUpcomingEvents(eventsResult.data);
      }
    } catch (err) {
      setError('Failed to fetch bed allocation data');
      console.error('Error fetching bed allocation data:', err);
    } finally {
      setIsLoading(false);
    }
  }, [hostelId]);

  // Initial data load
  useEffect(() => {
    refreshData();
  }, [refreshData]);

  // Set up real-time updates (in a real app, this would use WebSockets or Server-Sent Events)
  useEffect(() => {
    if (!hostelId) return;

    // Simulate real-time updates by polling every 30 seconds
    const interval = setInterval(() => {
      refreshData();
    }, 30000);

    return () => clearInterval(interval);
  }, [hostelId, refreshData]);

  // Listen for notification events that might affect bed allocation data
  useEffect(() => {
    const handleNotificationUpdate = (event: CustomEvent) => {
      const notification = event.detail;
      
      // Check if the notification is related to bed allocation
      if (notification.type === 'system' && 
          notification.metadata && 
          (notification.metadata.allocationId || notification.metadata.bedNumber)) {
        
        // Refresh data when bed allocation notifications are received
        refreshData();
      }
    };

    // Listen for custom notification events
    window.addEventListener('notification-received', handleNotificationUpdate as EventListener);

    return () => {
      window.removeEventListener('notification-received', handleNotificationUpdate as EventListener);
    };
  }, [refreshData]);

  return {
    beds,
    allocations,
    occupancySummary,
    upcomingEvents,
    isLoading,
    error,
    refreshData
  };
};

// Hook for monitoring bed status changes
export const useBedStatusMonitor = (bedId: string) => {
  const [bed, setBed] = useState<Bed | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const refreshBed = useCallback(async () => {
    if (!bedId) return;

    setIsLoading(true);
    try {
      const bedData = mockBeds.find(b => b.id === bedId);
      setBed(bedData || null);
    } catch (err) {
      console.error('Error fetching bed data:', err);
    } finally {
      setIsLoading(false);
    }
  }, [bedId]);

  useEffect(() => {
    refreshBed();
  }, [refreshBed]);

  // Monitor for bed status changes
  useEffect(() => {
    const interval = setInterval(refreshBed, 10000); // Check every 10 seconds
    return () => clearInterval(interval);
  }, [refreshBed]);

  return { bed, isLoading, refreshBed };
};

// Hook for allocation analytics
export const useAllocationAnalytics = (hostelId: string, timeRange: 'week' | 'month' | 'year' = 'month') => {
  const [analytics, setAnalytics] = useState({
    totalAllocations: 0,
    averageStayDuration: 0,
    occupancyTrend: [] as Array<{ date: string; occupancy: number }>,
    revenueData: [] as Array<{ date: string; revenue: number }>,
    topPerformingBeds: [] as Array<{ bedNumber: string; occupancyRate: number; revenue: number }>
  });
  const [isLoading, setIsLoading] = useState(false);

  const calculateAnalytics = useCallback(async () => {
    if (!hostelId) return;

    setIsLoading(true);
    try {
      const allocations = getAllocationsByHostelId(hostelId);
      const beds = getBedsByHostelId(hostelId);

      // Calculate total allocations
      const totalAllocations = allocations.length;

      // Calculate average stay duration
      const completedAllocations = allocations.filter(a => a.actualCheckOutDate);
      const averageStayDuration = completedAllocations.length > 0 
        ? completedAllocations.reduce((sum, allocation) => {
            const checkIn = new Date(allocation.checkInDate);
            const checkOut = new Date(allocation.actualCheckOutDate!);
            const duration = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24));
            return sum + duration;
          }, 0) / completedAllocations.length
        : 0;

      // Generate mock occupancy trend data
      const occupancyTrend = Array.from({ length: 30 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - (29 - i));
        return {
          date: date.toISOString().split('T')[0],
          occupancy: Math.floor(Math.random() * 40) + 60 // Random occupancy between 60-100%
        };
      });

      // Generate mock revenue data
      const revenueData = Array.from({ length: 30 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - (29 - i));
        return {
          date: date.toISOString().split('T')[0],
          revenue: Math.floor(Math.random() * 50000) + 30000 // Random revenue between 30k-80k
        };
      });

      // Calculate top performing beds
      const topPerformingBeds = beds.map(bed => {
        const bedAllocations = allocations.filter(a => a.bedId === bed.id);
        const occupancyRate = bedAllocations.length > 0 ? 
          (bedAllocations.filter(a => a.status === 'active').length / bedAllocations.length) * 100 : 0;
        const revenue = bedAllocations.reduce((sum, a) => sum + a.monthlyRent, 0);
        
        return {
          bedNumber: bed.bedNumber,
          occupancyRate,
          revenue
        };
      }).sort((a, b) => b.revenue - a.revenue).slice(0, 5);

      setAnalytics({
        totalAllocations,
        averageStayDuration,
        occupancyTrend,
        revenueData,
        topPerformingBeds
      });
    } catch (err) {
      console.error('Error calculating analytics:', err);
    } finally {
      setIsLoading(false);
    }
  }, [hostelId, timeRange]);

  useEffect(() => {
    calculateAnalytics();
  }, [calculateAnalytics]);

  return { analytics, isLoading, refreshAnalytics: calculateAnalytics };
};

// Utility function to emit custom notification events
export const emitNotificationEvent = (notification: any) => {
  const event = new CustomEvent('notification-received', { detail: notification });
  window.dispatchEvent(event);
};

// Utility function to check for upcoming check-outs and create notifications
export const checkUpcomingEvents = async (hostelId: string, ownerId: string) => {
  try {
    const eventsResult = await BedAllocationService.getUpcomingEvents(hostelId, 7);
    
    if (eventsResult.success && eventsResult.data) {
      eventsResult.data.forEach(event => {
        if (event.type === 'check-out') {
          const daysRemaining = Math.ceil(
            (new Date(event.date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
          );
          
          if (daysRemaining <= 3) {
            NotificationService.notifyUpcomingCheckOut(
              ownerId,
              `${event.tenant.firstName} ${event.tenant.lastName}`,
              event.bed.bedNumber,
              daysRemaining,
              event.allocation.id
            );
          }
        }
      });
    }
  } catch (error) {
    console.error('Error checking upcoming events:', error);
  }
};
