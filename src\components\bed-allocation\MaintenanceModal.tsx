import React, { useState } from 'react';
import { 
  Wrench, 
  Calendar, 
  AlertTriangle, 
  CheckCircle,
  Clock
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { Bed } from '@/data/mockData';

interface MaintenanceModalProps {
  bed: Bed;
  isOpen: boolean;
  onClose: () => void;
  onUpdateMaintenance: (maintenanceData: {
    status: 'maintenance' | 'available';
    maintenanceNotes?: string;
    lastMaintenanceDate?: string;
    nextMaintenanceDate?: string;
    estimatedCompletionDate?: string;
  }) => void;
}

export const MaintenanceModal: React.FC<MaintenanceModalProps> = ({
  bed,
  isOpen,
  onClose,
  onUpdateMaintenance
}) => {
  const { toast } = useToast();
  const [action, setAction] = useState<'mark_maintenance' | 'complete_maintenance'>(
    bed.status === 'maintenance' ? 'complete_maintenance' : 'mark_maintenance'
  );
  
  const [maintenanceData, setMaintenanceData] = useState({
    maintenanceType: '',
    priority: 'medium' as 'low' | 'medium' | 'high' | 'urgent',
    estimatedCompletionDate: '',
    maintenanceNotes: bed.maintenanceNotes || '',
    completionNotes: ''
  });

  const handleSubmit = () => {
    if (action === 'mark_maintenance') {
      onUpdateMaintenance({
        status: 'maintenance',
        maintenanceNotes: maintenanceData.maintenanceNotes,
        lastMaintenanceDate: new Date().toISOString().split('T')[0],
        nextMaintenanceDate: maintenanceData.estimatedCompletionDate
      });
      
      toast({
        title: 'Bed Marked for Maintenance',
        description: `Bed ${bed.bedNumber} has been marked for maintenance`,
      });
    } else {
      onUpdateMaintenance({
        status: 'available',
        maintenanceNotes: maintenanceData.completionNotes,
        lastMaintenanceDate: new Date().toISOString().split('T')[0]
      });
      
      toast({
        title: 'Maintenance Completed',
        description: `Bed ${bed.bedNumber} is now available for allocation`,
      });
    }

    onClose();
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Wrench className="h-5 w-5" />
            {action === 'mark_maintenance' ? 'Mark for Maintenance' : 'Complete Maintenance'} - Bed {bed.bedNumber}
          </DialogTitle>
          <DialogDescription>
            {action === 'mark_maintenance' 
              ? 'Mark this bed as under maintenance and provide details'
              : 'Mark maintenance as complete and make the bed available'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current Status */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Current Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Bed Status</p>
                  <Badge className={
                    bed.status === 'maintenance' 
                      ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                      : 'bg-green-100 text-green-800 border-green-200'
                  }>
                    {bed.status === 'maintenance' ? 'Under Maintenance' : 'Available'}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Bed Type</p>
                  <p className="capitalize">{bed.bedType.replace('_', ' ')}</p>
                </div>
                {bed.lastMaintenanceDate && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Last Maintenance</p>
                    <p>{new Date(bed.lastMaintenanceDate).toLocaleDateString()}</p>
                  </div>
                )}
                {bed.maintenanceNotes && (
                  <div className="col-span-2">
                    <p className="text-sm font-medium text-muted-foreground">Current Notes</p>
                    <p className="text-sm bg-muted p-2 rounded">{bed.maintenanceNotes}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Action Selection */}
          <Card>
            <CardHeader>
              <CardTitle>Action</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div
                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    action === 'mark_maintenance'
                      ? 'border-primary bg-primary/5'
                      : 'border-muted hover:border-muted-foreground/50'
                  }`}
                  onClick={() => setAction('mark_maintenance')}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <AlertTriangle className="h-5 w-5 text-yellow-600" />
                    <span className="font-medium">Mark for Maintenance</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Set bed status to maintenance and specify details
                  </p>
                </div>

                <div
                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    action === 'complete_maintenance'
                      ? 'border-primary bg-primary/5'
                      : 'border-muted hover:border-muted-foreground/50'
                  } ${bed.status !== 'maintenance' ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={() => bed.status === 'maintenance' && setAction('complete_maintenance')}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="font-medium">Complete Maintenance</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Mark maintenance as complete and make bed available
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Maintenance Details Form */}
          {action === 'mark_maintenance' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wrench className="h-5 w-5" />
                  Maintenance Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="maintenanceType">Maintenance Type</Label>
                    <Select 
                      value={maintenanceData.maintenanceType} 
                      onValueChange={(value) => setMaintenanceData(prev => ({
                        ...prev,
                        maintenanceType: value
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cleaning">Deep Cleaning</SelectItem>
                        <SelectItem value="bed_repair">Bed Repair</SelectItem>
                        <SelectItem value="mattress_replacement">Mattress Replacement</SelectItem>
                        <SelectItem value="electrical">Electrical Work</SelectItem>
                        <SelectItem value="plumbing">Plumbing</SelectItem>
                        <SelectItem value="painting">Painting</SelectItem>
                        <SelectItem value="furniture_repair">Furniture Repair</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="priority">Priority</Label>
                    <Select 
                      value={maintenanceData.priority} 
                      onValueChange={(value: any) => setMaintenanceData(prev => ({
                        ...prev,
                        priority: value
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="urgent">Urgent</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="estimatedCompletionDate">Estimated Completion Date</Label>
                  <Input
                    id="estimatedCompletionDate"
                    type="date"
                    value={maintenanceData.estimatedCompletionDate}
                    onChange={(e) => setMaintenanceData(prev => ({
                      ...prev,
                      estimatedCompletionDate: e.target.value
                    }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maintenanceNotes">Maintenance Notes</Label>
                  <Textarea
                    id="maintenanceNotes"
                    placeholder="Describe the maintenance issue and required work..."
                    value={maintenanceData.maintenanceNotes}
                    onChange={(e) => setMaintenanceData(prev => ({
                      ...prev,
                      maintenanceNotes: e.target.value
                    }))}
                    rows={4}
                  />
                </div>

                {/* Priority Indicator */}
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Priority Level:</span>
                  <Badge className={getPriorityColor(maintenanceData.priority)}>
                    {maintenanceData.priority.charAt(0).toUpperCase() + maintenanceData.priority.slice(1)}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Completion Details Form */}
          {action === 'complete_maintenance' && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5" />
                  Completion Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="completionNotes">Completion Notes</Label>
                  <Textarea
                    id="completionNotes"
                    placeholder="Describe the work completed and current condition of the bed..."
                    value={maintenanceData.completionNotes}
                    onChange={(e) => setMaintenanceData(prev => ({
                      ...prev,
                      completionNotes: e.target.value
                    }))}
                    rows={4}
                  />
                </div>

                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    Completing maintenance will make this bed available for new allocations.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleSubmit}
              disabled={
                action === 'mark_maintenance' && 
                (!maintenanceData.maintenanceType || !maintenanceData.maintenanceNotes.trim())
              }
            >
              {action === 'mark_maintenance' ? (
                <>
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Mark for Maintenance
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Complete Maintenance
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
