// Mock data for the hostel management system

export interface Hostel {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  pincode: string;
  totalBeds: number;
  availableBeds: number;
  pricePerBed: number;
  amenities: string[];
  rating: number;
  images: string[];
  ownerId: string;
  employees: string[];
  status: 'active' | 'inactive';
  approvalStatus: 'approved' | 'pending' | 'rejected';
  createdDate: string;
  approvedDate?: string;
  rejectedDate?: string;
  rejectionReason?: string;
}

// New interface for hostel registration requests
export interface HostelRegistration {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  pincode: string;
  description: string;
  totalBeds: number;
  pricePerBed: number;
  amenities: string[];
  images: string[];
  ownerId: string;
  ownerName: string;
  ownerEmail: string;
  ownerPhone: string;
  contactPerson: string;
  contactPhone: string;
  contactEmail: string;
  licenseNumber: string;
  gstNumber?: string;
  bankAccountDetails: {
    accountNumber: string;
    ifscCode: string;
    bankName: string;
    accountHolderName: string;
  };
  status: 'pending' | 'approved' | 'rejected';
  submittedDate: string;
  reviewedDate?: string;
  reviewedBy?: string;
  reviewComments?: string;
  rejectionReason?: string;
}

// New interface for floors
export interface Floor {
  id: string;
  hostelId: string;
  floorNumber: number;
  floorName: string;
  totalRooms: number;
  description?: string;
  amenities: string[];
  createdDate: string;
  updatedDate: string;
}

// New interface for rooms
export interface Room {
  id: string;
  hostelId: string;
  floorId: string;
  roomNumber: string;
  roomType: 'single' | 'double' | 'triple' | 'quad' | 'dormitory';
  capacity: number;
  currentOccupancy: number;
  pricePerBed: number;
  amenities: string[];
  description?: string;
  images: string[];
  status: 'available' | 'occupied' | 'maintenance' | 'inactive';
  dimensions?: {
    length: number;
    width: number;
    area: number;
  };
  furnishing: string[];
  createdDate: string;
  updatedDate: string;
}

// New interface for individual beds
export interface Bed {
  id: string;
  roomId: string;
  hostelId: string;
  bedNumber: string; // e.g., "A1", "B2", etc.
  bedType: 'single' | 'bunk_top' | 'bunk_bottom';
  status: 'available' | 'occupied' | 'maintenance' | 'reserved' | 'inactive';
  pricePerMonth: number;
  // Current allocation
  currentTenantId?: string;
  allocationDate?: string;
  checkInDate?: string;
  checkOutDate?: string;
  // Bed specifications
  bedSize: 'single' | 'double' | 'queen' | 'king';
  hasStorage: boolean;
  hasPrivacyCurtain: boolean;
  hasReadingLight: boolean;
  hasPowerOutlet: boolean;
  // Maintenance
  lastMaintenanceDate?: string;
  nextMaintenanceDate?: string;
  maintenanceNotes?: string;
  createdDate: string;
  updatedDate: string;
}

// Interface for bed allocations and tenant management
export interface BedAllocation {
  id: string;
  bedId: string;
  roomId: string;
  hostelId: string;
  tenantId: string;
  // Allocation details
  allocationDate: string;
  checkInDate: string;
  expectedCheckOutDate: string;
  actualCheckOutDate?: string;
  // Pricing and payment
  monthlyRent: number;
  securityDeposit: number;
  advancePayment: number;
  // Status and notes
  status: 'active' | 'expired' | 'terminated' | 'pending';
  allocationNotes?: string;
  terminationReason?: string;
  // Metadata
  allocatedBy: string; // Owner/admin user ID
  createdDate: string;
  updatedDate: string;
}

// Extended tenant interface for hostel context
export interface HostelTenant {
  id: string;
  // Personal information
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  gender: 'male' | 'female' | 'other';
  // Address
  permanentAddress: {
    street: string;
    city: string;
    state: string;
    pincode: string;
  };
  // Emergency contact
  emergencyContact: {
    name: string;
    relationship: string;
    phone: string;
  };
  // Professional information
  occupation: 'student' | 'working_professional' | 'freelancer' | 'other';
  company?: string;
  designation?: string;
  monthlyIncome?: number;
  // Documents
  documents: {
    identityProof: string;
    addressProof: string;
    incomeProof?: string;
    photo: string;
  };
  // Hostel-specific information
  preferredRoomType?: Room['roomType'];
  budgetRange: {
    min: number;
    max: number;
  };
  // Status and dates
  status: 'active' | 'inactive' | 'blacklisted';
  registrationDate: string;
  lastActiveDate: string;
  // Current allocation
  currentBedId?: string;
  currentRoomId?: string;
  currentHostelId?: string;
}

// New interface for account requests
export interface AccountRequest {
  id: string;
  // Personal Information
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  address: {
    street: string;
    city: string;
    state: string;
    pincode: string;
  };
  // Business Information
  businessName: string;
  businessType: 'individual' | 'partnership' | 'company' | 'llp';
  businessLicense: string;
  gstNumber?: string;
  panNumber: string;
  // Documents
  documents: {
    identityProof: string; // File path/URL
    addressProof: string; // File path/URL
    businessLicense: string; // File path/URL
    panCard: string; // File path/URL
    gstCertificate?: string; // File path/URL
  };
  // Request Details
  reasonForRequest: string;
  experienceInHospitality?: string;
  numberOfPropertiesPlanned: number;
  estimatedInvestment?: number;
  // Status and Tracking
  status: 'pending' | 'approved' | 'rejected';
  submittedDate: string;
  reviewedDate?: string;
  reviewedBy?: string;
  reviewComments?: string;
  rejectionReason?: string;
  // Generated Account Details (when approved)
  generatedUserId?: string;
  generatedUsername?: string;
  temporaryPassword?: string;
  accountCreatedDate?: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: 'superadmin' | 'owner' | 'employee' | 'member';
  hostelId?: string;
  avatar?: string;
  joinedDate: string;
  status: 'active' | 'inactive';
}

export interface Booking {
  id: string;
  userId: string;
  hostelId: string;
  bedNumber: string;
  checkIn: string;
  checkOut: string;
  amount: number;
  status: 'confirmed' | 'pending' | 'cancelled';
  paymentStatus: 'paid' | 'pending' | 'failed';
}

export interface Payment {
  id: string;
  bookingId: string;
  userId: string;
  amount: number;
  date: string;
  method: 'card' | 'upi' | 'bank_transfer';
  status: 'success' | 'pending' | 'failed';
  splitDetails: {
    hostelOwner: number;
    platform: number;
  };
}

export interface Complaint {
  id: string;
  userId: string;
  hostelId: string;
  title: string;
  description: string;
  category: 'maintenance' | 'cleanliness' | 'noise' | 'service' | 'other';
  status: 'open' | 'in_progress' | 'resolved';
  priority: 'low' | 'medium' | 'high';
  createdDate: string;
  resolvedDate?: string;
}

// Mock Data
export const mockHostels: Hostel[] = [
  {
    id: '1',
    name: 'Urban Stay Hostel',
    address: '123 College Road',
    city: 'Mumbai',
    state: 'Maharashtra',
    pincode: '400001',
    totalBeds: 100,
    availableBeds: 25,
    pricePerBed: 8000,
    amenities: ['WiFi', 'AC', 'Laundry', 'Mess', 'Security'],
    rating: 4.5,
    images: ['/placeholder.svg'],
    ownerId: '2',
    employees: ['3', '4'],
    status: 'active',
    approvalStatus: 'approved',
    createdDate: '2023-02-15',
    approvedDate: '2023-02-20'
  },
  {
    id: '2',
    name: 'Campus Lodge',
    address: '456 University Lane',
    city: 'Pune',
    state: 'Maharashtra',
    pincode: '411001',
    totalBeds: 80,
    availableBeds: 15,
    pricePerBed: 7500,
    amenities: ['WiFi', 'Gym', 'Mess', 'Study Room', 'Parking'],
    rating: 4.2,
    images: ['/placeholder.svg'],
    ownerId: '5',
    employees: ['6'],
    status: 'active',
    approvalStatus: 'approved',
    createdDate: '2023-03-01',
    approvedDate: '2023-03-05'
  },
  {
    id: '3',
    name: 'Elite Residency',
    address: '789 Tech Park',
    city: 'Bangalore',
    state: 'Karnataka',
    pincode: '560001',
    totalBeds: 150,
    availableBeds: 45,
    pricePerBed: 12000,
    amenities: ['WiFi', 'AC', 'Gym', 'Swimming Pool', 'Cafeteria', 'Security'],
    rating: 4.8,
    images: ['/placeholder.svg'],
    ownerId: '7',
    employees: ['8', '9', '10'],
    status: 'active',
    approvalStatus: 'approved',
    createdDate: '2023-03-10',
    approvedDate: '2023-03-15'
  }
];

// Mock data for hostel registrations
export const mockHostelRegistrations: HostelRegistration[] = [
  {
    id: 'reg-1',
    name: 'Green Valley Hostel',
    address: '456 Garden Street',
    city: 'Chennai',
    state: 'Tamil Nadu',
    pincode: '600001',
    description: 'A modern hostel with eco-friendly amenities and excellent connectivity to major colleges and IT parks.',
    totalBeds: 120,
    pricePerBed: 9500,
    amenities: ['WiFi', 'AC', 'Solar Power', 'Organic Mess', 'Library', 'Security'],
    images: ['/placeholder.svg', '/placeholder.svg'],
    ownerId: '13',
    ownerName: 'Arjun Reddy',
    ownerEmail: '<EMAIL>',
    ownerPhone: '+91 **********',
    contactPerson: 'Arjun Reddy',
    contactPhone: '+91 **********',
    contactEmail: '<EMAIL>',
    licenseNumber: 'TN-HST-2024-001',
    gstNumber: '33**********1Z5',
    bankAccountDetails: {
      accountNumber: '**********',
      ifscCode: 'HDFC0001234',
      bankName: 'HDFC Bank',
      accountHolderName: 'Arjun Reddy'
    },
    status: 'pending',
    submittedDate: '2024-01-15'
  },
  {
    id: 'reg-2',
    name: 'Tech Hub Residency',
    address: '789 Innovation Drive',
    city: 'Hyderabad',
    state: 'Telangana',
    pincode: '500001',
    description: 'Premium hostel designed for tech professionals and students with state-of-the-art facilities.',
    totalBeds: 200,
    pricePerBed: 11000,
    amenities: ['WiFi', 'AC', 'Coworking Space', 'Gym', 'Cafeteria', '24/7 Security', 'Parking'],
    images: ['/placeholder.svg', '/placeholder.svg', '/placeholder.svg'],
    ownerId: '14',
    ownerName: 'Priya Sharma',
    ownerEmail: '<EMAIL>',
    ownerPhone: '+91 **********',
    contactPerson: 'Priya Sharma',
    contactPhone: '+91 **********',
    contactEmail: '<EMAIL>',
    licenseNumber: 'TS-HST-2024-002',
    gstNumber: '36**********2L9',
    bankAccountDetails: {
      accountNumber: '**********',
      ifscCode: 'ICICI0005678',
      bankName: 'ICICI Bank',
      accountHolderName: 'Priya Sharma'
    },
    status: 'pending',
    submittedDate: '2024-01-20'
  },
  {
    id: 'reg-3',
    name: 'Budget Stay Inn',
    address: '321 Student Colony',
    city: 'Jaipur',
    state: 'Rajasthan',
    pincode: '302001',
    description: 'Affordable accommodation for students with basic amenities and good connectivity.',
    totalBeds: 60,
    pricePerBed: 6000,
    amenities: ['WiFi', 'Mess', 'Study Room', 'Security'],
    images: ['/placeholder.svg'],
    ownerId: '15',
    ownerName: 'Vikram Singh',
    ownerEmail: '<EMAIL>',
    ownerPhone: '+91 **********',
    contactPerson: 'Vikram Singh',
    contactPhone: '+91 **********',
    contactEmail: '<EMAIL>',
    licenseNumber: 'RJ-HST-2024-003',
    bankAccountDetails: {
      accountNumber: '**********',
      ifscCode: '**********',
      bankName: 'State Bank of India',
      accountHolderName: 'Vikram Singh'
    },
    status: 'rejected',
    submittedDate: '2024-01-10',
    reviewedDate: '2024-01-18',
    reviewedBy: '1',
    rejectionReason: 'Insufficient safety measures and documentation incomplete.'
  }
];

export const mockUsers: User[] = [
  {
    id: '1',
    name: 'Admin User',
    email: '<EMAIL>',
    phone: '+91 **********',
    role: 'superadmin',
    joinedDate: '2023-01-01',
    status: 'active'
  },
  {
    id: '2',
    name: 'Rajesh Kumar',
    email: '<EMAIL>',
    phone: '+91 **********',
    role: 'owner',
    hostelId: '1',
    joinedDate: '2023-02-15',
    status: 'active'
  },
  {
    id: '3',
    name: 'Priya Sharma',
    email: '<EMAIL>',
    phone: '+91 **********',
    role: 'employee',
    hostelId: '1',
    joinedDate: '2023-03-10',
    status: 'active'
  },
  {
    id: '4',
    name: 'Amit Singh',
    email: '<EMAIL>',
    phone: '+91 **********',
    role: 'employee',
    hostelId: '1',
    joinedDate: '2023-03-15',
    status: 'active'
  },
  {
    id: '11',
    name: 'Rohit Verma',
    email: '<EMAIL>',
    phone: '+91 9876543220',
    role: 'member',
    joinedDate: '2023-06-01',
    status: 'active'
  },
  {
    id: '12',
    name: 'Sneha Patel',
    email: '<EMAIL>',
    phone: '+91 9876543221',
    role: 'member',
    joinedDate: '2023-06-15',
    status: 'active'
  },
  {
    id: '13',
    name: 'Arjun Reddy',
    email: '<EMAIL>',
    phone: '+91 **********',
    role: 'owner',
    joinedDate: '2024-01-10',
    status: 'active'
  },
  {
    id: '14',
    name: 'Priya Sharma',
    email: '<EMAIL>',
    phone: '+91 **********',
    role: 'owner',
    joinedDate: '2024-01-15',
    status: 'active'
  },
  {
    id: '15',
    name: 'Vikram Singh',
    email: '<EMAIL>',
    phone: '+91 **********',
    role: 'owner',
    joinedDate: '2024-01-05',
    status: 'active'
  },
  {
    id: '16',
    name: 'Meera Patel',
    email: '<EMAIL>',
    phone: '+91 **********',
    role: 'owner',
    joinedDate: '2024-01-22',
    status: 'active'
  }
];

export const mockBookings: Booking[] = [
  {
    id: '1',
    userId: '11',
    hostelId: '1',
    bedNumber: 'A101',
    checkIn: '2024-01-01',
    checkOut: '2024-06-30',
    amount: 48000,
    status: 'confirmed',
    paymentStatus: 'paid'
  },
  {
    id: '2',
    userId: '12',
    hostelId: '3',
    bedNumber: 'B205',
    checkIn: '2024-02-01',
    checkOut: '2024-07-31',
    amount: 72000,
    status: 'confirmed',
    paymentStatus: 'paid'
  }
];

export const mockPayments: Payment[] = [
  {
    id: '1',
    bookingId: '1',
    userId: '11',
    amount: 48000,
    date: '2023-12-25',
    method: 'upi',
    status: 'success',
    splitDetails: {
      hostelOwner: 45600,
      platform: 2400
    }
  },
  {
    id: '2',
    bookingId: '2',
    userId: '12',
    amount: 72000,
    date: '2024-01-25',
    method: 'card',
    status: 'success',
    splitDetails: {
      hostelOwner: 68400,
      platform: 3600
    }
  }
];

export const mockComplaints: Complaint[] = [
  {
    id: '1',
    userId: '11',
    hostelId: '1',
    title: 'AC not working in room A101',
    description: 'The air conditioning unit has stopped working since yesterday. The room is getting very hot.',
    category: 'maintenance',
    status: 'in_progress',
    priority: 'high',
    createdDate: '2024-01-15'
  },
  {
    id: '2',
    userId: '12',
    hostelId: '3',
    title: 'Noisy neighbors',
    description: 'There is excessive noise from the room next door during late night hours.',
    category: 'noise',
    status: 'open',
    priority: 'medium',
    createdDate: '2024-01-20'
  }
];

// Mock data for floors
export const mockFloors: Floor[] = [
  {
    id: 'floor-1',
    hostelId: '1',
    floorNumber: 1,
    floorName: 'Ground Floor',
    totalRooms: 10,
    description: 'Ground floor with reception, common area, and rooms',
    amenities: ['Reception', 'Common Room', 'Dining Area'],
    createdDate: '2023-02-25',
    updatedDate: '2023-02-25'
  },
  {
    id: 'floor-2',
    hostelId: '1',
    floorNumber: 2,
    floorName: 'First Floor',
    totalRooms: 15,
    description: 'First floor with standard rooms',
    amenities: ['Study Room', 'Laundry Room'],
    createdDate: '2023-02-25',
    updatedDate: '2023-02-25'
  },
  {
    id: 'floor-3',
    hostelId: '2',
    floorNumber: 1,
    floorName: 'Ground Floor',
    totalRooms: 8,
    description: 'Ground floor with reception and premium rooms',
    amenities: ['Reception', 'Lounge', 'Cafeteria'],
    createdDate: '2023-03-10',
    updatedDate: '2023-03-10'
  }
];

// Mock data for rooms
export const mockRooms: Room[] = [
  {
    id: 'room-1',
    hostelId: '1',
    floorId: 'floor-1',
    roomNumber: 'G-101',
    roomType: 'double',
    capacity: 2,
    currentOccupancy: 1,
    pricePerBed: 8000,
    amenities: ['AC', 'Attached Bathroom', 'Study Table', 'Wardrobe'],
    description: 'Spacious double room with all basic amenities',
    images: ['/placeholder.svg'],
    status: 'available',
    dimensions: {
      length: 12,
      width: 10,
      area: 120
    },
    furnishing: ['Bed', 'Mattress', 'Study Table', 'Chair', 'Wardrobe', 'Bookshelf'],
    createdDate: '2023-02-26',
    updatedDate: '2023-02-26'
  },
  {
    id: 'room-2',
    hostelId: '1',
    floorId: 'floor-1',
    roomNumber: 'G-102',
    roomType: 'single',
    capacity: 1,
    currentOccupancy: 1,
    pricePerBed: 10000,
    amenities: ['AC', 'Attached Bathroom', 'Study Table', 'Wardrobe', 'Mini Fridge'],
    description: 'Premium single room with additional amenities',
    images: ['/placeholder.svg'],
    status: 'occupied',
    dimensions: {
      length: 10,
      width: 8,
      area: 80
    },
    furnishing: ['Bed', 'Mattress', 'Study Table', 'Chair', 'Wardrobe', 'Bookshelf', 'Sofa'],
    createdDate: '2023-02-26',
    updatedDate: '2023-02-26'
  },
  {
    id: 'room-3',
    hostelId: '1',
    floorId: 'floor-2',
    roomNumber: 'F-201',
    roomType: 'triple',
    capacity: 3,
    currentOccupancy: 2,
    pricePerBed: 7500,
    amenities: ['AC', 'Attached Bathroom', 'Study Tables', 'Wardrobes'],
    description: 'Triple sharing room with comfortable space',
    images: ['/placeholder.svg'],
    status: 'available',
    dimensions: {
      length: 16,
      width: 14,
      area: 224
    },
    furnishing: ['Beds', 'Mattresses', 'Study Tables', 'Chairs', 'Wardrobes'],
    createdDate: '2023-02-26',
    updatedDate: '2023-02-26'
  },
  {
    id: 'room-4',
    hostelId: '2',
    floorId: 'floor-3',
    roomNumber: 'G-101',
    roomType: 'double',
    capacity: 2,
    currentOccupancy: 0,
    pricePerBed: 7500,
    amenities: ['AC', 'Attached Bathroom', 'Study Table', 'Wardrobe'],
    description: 'Standard double room with all necessary amenities',
    images: ['/placeholder.svg'],
    status: 'available',
    dimensions: {
      length: 12,
      width: 10,
      area: 120
    },
    furnishing: ['Bed', 'Mattress', 'Study Table', 'Chair', 'Wardrobe'],
    createdDate: '2023-03-12',
    updatedDate: '2023-03-12'
  }
];

// Helper functions
export const getUserById = (id: string) => mockUsers.find(user => user.id === id);
export const getHostelById = (id: string) => mockHostels.find(hostel => hostel.id === id);
export const getBookingsByUserId = (userId: string) => mockBookings.filter(booking => booking.userId === userId);
export const getComplaintsByHostelId = (hostelId: string) => mockComplaints.filter(complaint => complaint.hostelId === hostelId);
export const getHostelRegistrationById = (id: string) => mockHostelRegistrations.find(reg => reg.id === id);
export const getHostelRegistrationsByOwnerId = (ownerId: string) => mockHostelRegistrations.filter(reg => reg.ownerId === ownerId);
export const getFloorsByHostelId = (hostelId: string) => mockFloors.filter(floor => floor.hostelId === hostelId);
export const getRoomsByFloorId = (floorId: string) => mockRooms.filter(room => room.floorId === floorId);
export const getRoomsByHostelId = (hostelId: string) => mockRooms.filter(room => room.hostelId === hostelId);

// Mock data for account requests
export const mockAccountRequests: AccountRequest[] = [
  {
    id: 'acc-req-1',
    firstName: 'Rahul',
    lastName: 'Sharma',
    email: '<EMAIL>',
    phone: '+91 **********',
    dateOfBirth: '1985-03-15',
    address: {
      street: '123 Business District, Sector 18',
      city: 'Gurgaon',
      state: 'Haryana',
      pincode: '122001'
    },
    businessName: 'Sharma Hospitality Services',
    businessType: 'company',
    businessLicense: 'HR-BL-2024-001',
    gstNumber: '06**********1Z5',
    panNumber: '**********',
    documents: {
      identityProof: '/documents/rahul-aadhar.pdf',
      addressProof: '/documents/rahul-address.pdf',
      businessLicense: '/documents/sharma-business-license.pdf',
      panCard: '/documents/rahul-pan.pdf',
      gstCertificate: '/documents/sharma-gst.pdf'
    },
    reasonForRequest: 'I want to expand my hospitality business by partnering with Room Buddy Hub to manage multiple hostel properties in the NCR region. I have 5 years of experience in the hospitality industry.',
    experienceInHospitality: '5 years in hotel and guest house management',
    numberOfPropertiesPlanned: 3,
    estimatedInvestment: 5000000,
    status: 'pending',
    submittedDate: '2024-01-25'
  },
  {
    id: 'acc-req-2',
    firstName: 'Meera',
    lastName: 'Patel',
    email: '<EMAIL>',
    phone: '+91 **********',
    dateOfBirth: '1990-07-22',
    address: {
      street: '456 Commercial Complex, Vastrapur',
      city: 'Ahmedabad',
      state: 'Gujarat',
      pincode: '380015'
    },
    businessName: 'Patel Properties',
    businessType: 'partnership',
    businessLicense: 'GJ-BL-2024-002',
    gstNumber: '24**********2L9',
    panNumber: '**********',
    documents: {
      identityProof: '/documents/meera-aadhar.pdf',
      addressProof: '/documents/meera-address.pdf',
      businessLicense: '/documents/patel-business-license.pdf',
      panCard: '/documents/meera-pan.pdf',
      gstCertificate: '/documents/patel-gst.pdf'
    },
    reasonForRequest: 'Looking to convert my existing rental properties into managed hostels for students and working professionals. I believe Room Buddy Hub platform will help me reach more customers.',
    experienceInHospitality: '2 years in property rental management',
    numberOfPropertiesPlanned: 2,
    estimatedInvestment: 2500000,
    status: 'approved',
    submittedDate: '2024-01-20',
    reviewedDate: '2024-01-22',
    reviewedBy: '1',
    reviewComments: 'Good business plan and proper documentation. Approved for account creation.',
    generatedUserId: '16',
    generatedUsername: 'meera.patel',
    temporaryPassword: 'TempPass123!',
    accountCreatedDate: '2024-01-22'
  },
  {
    id: 'acc-req-3',
    firstName: 'Karthik',
    lastName: 'Reddy',
    email: '<EMAIL>',
    phone: '+91 **********',
    dateOfBirth: '1988-11-10',
    address: {
      street: '789 IT Corridor, Madhapur',
      city: 'Hyderabad',
      state: 'Telangana',
      pincode: '500081'
    },
    businessName: 'Reddy Accommodations',
    businessType: 'individual',
    businessLicense: 'TS-BL-2024-003',
    panNumber: '**********',
    documents: {
      identityProof: '/documents/karthik-aadhar.pdf',
      addressProof: '/documents/karthik-address.pdf',
      businessLicense: '/documents/reddy-business-license.pdf',
      panCard: '/documents/karthik-pan.pdf'
    },
    reasonForRequest: 'I am a software engineer looking to start a side business in hospitality. I want to create premium hostels for IT professionals in Hyderabad.',
    experienceInHospitality: 'No prior experience, but have researched the market extensively',
    numberOfPropertiesPlanned: 1,
    estimatedInvestment: 1500000,
    status: 'rejected',
    submittedDate: '2024-01-18',
    reviewedDate: '2024-01-21',
    reviewedBy: '1',
    rejectionReason: 'Insufficient experience in hospitality industry and incomplete business documentation.',
    reviewComments: 'Please gain some experience in hospitality management and reapply with a more detailed business plan.'
  }
];

// Mock data for hostel tenants
export const mockHostelTenants: HostelTenant[] = [
  {
    id: 'tenant-1',
    firstName: 'Amit',
    lastName: 'Sharma',
    email: '<EMAIL>',
    phone: '+91 **********',
    dateOfBirth: '1998-05-15',
    gender: 'male',
    permanentAddress: {
      street: '123 Main Street, Sector 15',
      city: 'Gurgaon',
      state: 'Haryana',
      pincode: '122001'
    },
    emergencyContact: {
      name: 'Rajesh Sharma',
      relationship: 'Father',
      phone: '+91 **********'
    },
    occupation: 'working_professional',
    company: 'Tech Solutions Pvt Ltd',
    designation: 'Software Engineer',
    monthlyIncome: 45000,
    documents: {
      identityProof: '/documents/amit-aadhar.pdf',
      addressProof: '/documents/amit-address.pdf',
      incomeProof: '/documents/amit-salary.pdf',
      photo: '/documents/amit-photo.jpg'
    },
    preferredRoomType: 'double',
    budgetRange: { min: 8000, max: 12000 },
    status: 'active',
    registrationDate: '2024-01-15',
    lastActiveDate: '2024-01-28',
    currentBedId: 'bed-1',
    currentRoomId: 'room-1',
    currentHostelId: '1'
  },
  {
    id: 'tenant-2',
    firstName: 'Priya',
    lastName: 'Singh',
    email: '<EMAIL>',
    phone: '+91 **********',
    dateOfBirth: '1999-08-22',
    gender: 'female',
    permanentAddress: {
      street: '456 Park Avenue, Model Town',
      city: 'Delhi',
      state: 'Delhi',
      pincode: '110009'
    },
    emergencyContact: {
      name: 'Sunita Singh',
      relationship: 'Mother',
      phone: '+91 **********'
    },
    occupation: 'student',
    company: 'Delhi University',
    designation: 'MBA Student',
    documents: {
      identityProof: '/documents/priya-aadhar.pdf',
      addressProof: '/documents/priya-address.pdf',
      photo: '/documents/priya-photo.jpg'
    },
    preferredRoomType: 'triple',
    budgetRange: { min: 6000, max: 9000 },
    status: 'active',
    registrationDate: '2024-01-20',
    lastActiveDate: '2024-01-28',
    currentBedId: 'bed-2',
    currentRoomId: 'room-1',
    currentHostelId: '1'
  },
  {
    id: 'tenant-3',
    firstName: 'Rahul',
    lastName: 'Patel',
    email: '<EMAIL>',
    phone: '+91 9876543214',
    dateOfBirth: '1997-12-10',
    gender: 'male',
    permanentAddress: {
      street: '789 Commercial Street, Koramangala',
      city: 'Bangalore',
      state: 'Karnataka',
      pincode: '560034'
    },
    emergencyContact: {
      name: 'Kiran Patel',
      relationship: 'Brother',
      phone: '+91 9876543215'
    },
    occupation: 'working_professional',
    company: 'Infosys Limited',
    designation: 'Senior Developer',
    monthlyIncome: 65000,
    documents: {
      identityProof: '/documents/rahul-aadhar.pdf',
      addressProof: '/documents/rahul-address.pdf',
      incomeProof: '/documents/rahul-salary.pdf',
      photo: '/documents/rahul-photo.jpg'
    },
    preferredRoomType: 'single',
    budgetRange: { min: 10000, max: 15000 },
    status: 'active',
    registrationDate: '2024-01-10',
    lastActiveDate: '2024-01-28',
    currentBedId: 'bed-5',
    currentRoomId: 'room-2',
    currentHostelId: '2'
  }
];

// Mock data for beds
export const mockBeds: Bed[] = [
  // Beds for Room 1 (Urban Stay Hostel - Ground Floor - Room G-101)
  {
    id: 'bed-1',
    roomId: 'room-1',
    hostelId: '1',
    bedNumber: 'A1',
    bedType: 'bunk_bottom',
    status: 'occupied',
    pricePerMonth: 8500,
    currentTenantId: 'tenant-1',
    allocationDate: '2024-01-15',
    checkInDate: '2024-01-15',
    bedSize: 'single',
    hasStorage: true,
    hasPrivacyCurtain: true,
    hasReadingLight: true,
    hasPowerOutlet: true,
    createdDate: '2024-01-01',
    updatedDate: '2024-01-15'
  },
  {
    id: 'bed-2',
    roomId: 'room-1',
    hostelId: '1',
    bedNumber: 'A2',
    bedType: 'bunk_top',
    status: 'occupied',
    pricePerMonth: 8000,
    currentTenantId: 'tenant-2',
    allocationDate: '2024-01-20',
    checkInDate: '2024-01-20',
    bedSize: 'single',
    hasStorage: false,
    hasPrivacyCurtain: true,
    hasReadingLight: true,
    hasPowerOutlet: false,
    createdDate: '2024-01-01',
    updatedDate: '2024-01-20'
  },
  {
    id: 'bed-3',
    roomId: 'room-1',
    hostelId: '1',
    bedNumber: 'B1',
    bedType: 'bunk_bottom',
    status: 'available',
    pricePerMonth: 8500,
    bedSize: 'single',
    hasStorage: true,
    hasPrivacyCurtain: true,
    hasReadingLight: true,
    hasPowerOutlet: true,
    createdDate: '2024-01-01',
    updatedDate: '2024-01-01'
  },
  {
    id: 'bed-4',
    roomId: 'room-1',
    hostelId: '1',
    bedNumber: 'B2',
    bedType: 'bunk_top',
    status: 'maintenance',
    pricePerMonth: 8000,
    bedSize: 'single',
    hasStorage: false,
    hasPrivacyCurtain: true,
    hasReadingLight: false,
    hasPowerOutlet: false,
    lastMaintenanceDate: '2024-01-25',
    maintenanceNotes: 'Reading light needs repair',
    createdDate: '2024-01-01',
    updatedDate: '2024-01-25'
  },
  // Beds for Room 2 (Green Valley Hostel - First Floor - Room F1-201)
  {
    id: 'bed-5',
    roomId: 'room-2',
    hostelId: '2',
    bedNumber: 'S1',
    bedType: 'single',
    status: 'occupied',
    pricePerMonth: 12000,
    currentTenantId: 'tenant-3',
    allocationDate: '2024-01-10',
    checkInDate: '2024-01-10',
    bedSize: 'single',
    hasStorage: true,
    hasPrivacyCurtain: false,
    hasReadingLight: true,
    hasPowerOutlet: true,
    createdDate: '2024-01-01',
    updatedDate: '2024-01-10'
  },
  {
    id: 'bed-6',
    roomId: 'room-3',
    hostelId: '1',
    bedNumber: 'A1',
    bedType: 'bunk_bottom',
    status: 'available',
    pricePerMonth: 9000,
    bedSize: 'single',
    hasStorage: true,
    hasPrivacyCurtain: true,
    hasReadingLight: true,
    hasPowerOutlet: true,
    createdDate: '2024-01-01',
    updatedDate: '2024-01-01'
  }
];

// Mock data for bed allocations
export const mockBedAllocations: BedAllocation[] = [
  {
    id: 'alloc-1',
    bedId: 'bed-1',
    roomId: 'room-1',
    hostelId: '1',
    tenantId: 'tenant-1',
    allocationDate: '2024-01-15',
    checkInDate: '2024-01-15',
    expectedCheckOutDate: '2024-07-15',
    monthlyRent: 8500,
    securityDeposit: 17000,
    advancePayment: 8500,
    status: 'active',
    allocationNotes: 'Regular tenant, good payment history',
    allocatedBy: '2',
    createdDate: '2024-01-15',
    updatedDate: '2024-01-15'
  },
  {
    id: 'alloc-2',
    bedId: 'bed-2',
    roomId: 'room-1',
    hostelId: '1',
    tenantId: 'tenant-2',
    allocationDate: '2024-01-20',
    checkInDate: '2024-01-20',
    expectedCheckOutDate: '2024-06-20',
    monthlyRent: 8000,
    securityDeposit: 16000,
    advancePayment: 8000,
    status: 'active',
    allocationNotes: 'Student tenant, semester-based stay',
    allocatedBy: '2',
    createdDate: '2024-01-20',
    updatedDate: '2024-01-20'
  },
  {
    id: 'alloc-3',
    bedId: 'bed-5',
    roomId: 'room-2',
    hostelId: '2',
    tenantId: 'tenant-3',
    allocationDate: '2024-01-10',
    checkInDate: '2024-01-10',
    expectedCheckOutDate: '2024-12-10',
    monthlyRent: 12000,
    securityDeposit: 24000,
    advancePayment: 12000,
    status: 'active',
    allocationNotes: 'Long-term professional tenant',
    allocatedBy: '3',
    createdDate: '2024-01-10',
    updatedDate: '2024-01-10'
  }
];

// Helper functions for account requests
export const getAccountRequestById = (id: string) => mockAccountRequests.find(req => req.id === id);
export const getAccountRequestsByStatus = (status: AccountRequest['status']) => mockAccountRequests.filter(req => req.status === status);
export const getPendingAccountRequests = () => mockAccountRequests.filter(req => req.status === 'pending');
export const getApprovedAccountRequests = () => mockAccountRequests.filter(req => req.status === 'approved');
export const getRejectedAccountRequests = () => mockAccountRequests.filter(req => req.status === 'rejected');

// Helper functions for bed management
export const getBedsByRoomId = (roomId: string) => mockBeds.filter(bed => bed.roomId === roomId);
export const getBedsByHostelId = (hostelId: string) => mockBeds.filter(bed => bed.hostelId === hostelId);
export const getBedById = (bedId: string) => mockBeds.find(bed => bed.id === bedId);
export const getAvailableBedsByHostelId = (hostelId: string) => mockBeds.filter(bed => bed.hostelId === hostelId && bed.status === 'available');
export const getOccupiedBedsByHostelId = (hostelId: string) => mockBeds.filter(bed => bed.hostelId === hostelId && bed.status === 'occupied');

// Helper functions for tenant management
export const getHostelTenantById = (tenantId: string) => mockHostelTenants.find(tenant => tenant.id === tenantId);
export const getTenantsByHostelId = (hostelId: string) => mockHostelTenants.filter(tenant => tenant.currentHostelId === hostelId);
export const getActiveTenantsByHostelId = (hostelId: string) => mockHostelTenants.filter(tenant => tenant.currentHostelId === hostelId && tenant.status === 'active');

// Helper functions for bed allocations
export const getAllocationById = (allocationId: string) => mockBedAllocations.find(alloc => alloc.id === allocationId);
export const getAllocationsByHostelId = (hostelId: string) => mockBedAllocations.filter(alloc => alloc.hostelId === hostelId);
export const getAllocationsByTenantId = (tenantId: string) => mockBedAllocations.filter(alloc => alloc.tenantId === tenantId);
export const getActiveAllocationsByHostelId = (hostelId: string) => mockBedAllocations.filter(alloc => alloc.hostelId === hostelId && alloc.status === 'active');
export const getAllocationByBedId = (bedId: string) => mockBedAllocations.find(alloc => alloc.bedId === bedId && alloc.status === 'active');

// Helper functions for room occupancy calculations
export const getRoomOccupancyStatus = (roomId: string) => {
  const beds = getBedsByRoomId(roomId);
  const occupiedBeds = beds.filter(bed => bed.status === 'occupied').length;
  const totalBeds = beds.length;
  const availableBeds = beds.filter(bed => bed.status === 'available').length;
  const maintenanceBeds = beds.filter(bed => bed.status === 'maintenance').length;

  return {
    totalBeds,
    occupiedBeds,
    availableBeds,
    maintenanceBeds,
    occupancyRate: totalBeds > 0 ? (occupiedBeds / totalBeds) * 100 : 0
  };
};

// Helper function to get hostel occupancy summary
export const getHostelOccupancySummary = (hostelId: string) => {
  const beds = getBedsByHostelId(hostelId);
  const rooms = getRoomsByHostelId(hostelId);

  const totalBeds = beds.length;
  const occupiedBeds = beds.filter(bed => bed.status === 'occupied').length;
  const availableBeds = beds.filter(bed => bed.status === 'available').length;
  const maintenanceBeds = beds.filter(bed => bed.status === 'maintenance').length;
  const reservedBeds = beds.filter(bed => bed.status === 'reserved').length;

  const totalRooms = rooms.length;
  const fullyOccupiedRooms = rooms.filter(room => {
    const roomBeds = getBedsByRoomId(room.id);
    return roomBeds.every(bed => bed.status === 'occupied');
  }).length;

  const availableRooms = rooms.filter(room => {
    const roomBeds = getBedsByRoomId(room.id);
    return roomBeds.some(bed => bed.status === 'available');
  }).length;

  return {
    totalBeds,
    occupiedBeds,
    availableBeds,
    maintenanceBeds,
    reservedBeds,
    totalRooms,
    fullyOccupiedRooms,
    availableRooms,
    occupancyRate: totalBeds > 0 ? (occupiedBeds / totalBeds) * 100 : 0
  };
};