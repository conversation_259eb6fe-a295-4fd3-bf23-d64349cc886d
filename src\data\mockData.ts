// Mock data for the hostel management system

export interface Hostel {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  pincode: string;
  totalBeds: number;
  availableBeds: number;
  pricePerBed: number;
  amenities: string[];
  rating: number;
  images: string[];
  ownerId: string;
  employees: string[];
  status: 'active' | 'inactive';
  approvalStatus: 'approved' | 'pending' | 'rejected';
  createdDate: string;
  approvedDate?: string;
  rejectedDate?: string;
  rejectionReason?: string;
}

// New interface for hostel registration requests
export interface HostelRegistration {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  pincode: string;
  description: string;
  totalBeds: number;
  pricePerBed: number;
  amenities: string[];
  images: string[];
  ownerId: string;
  ownerName: string;
  ownerEmail: string;
  ownerPhone: string;
  contactPerson: string;
  contactPhone: string;
  contactEmail: string;
  licenseNumber: string;
  gstNumber?: string;
  bankAccountDetails: {
    accountNumber: string;
    ifscCode: string;
    bankName: string;
    accountHolderName: string;
  };
  status: 'pending' | 'approved' | 'rejected';
  submittedDate: string;
  reviewedDate?: string;
  reviewedBy?: string;
  reviewComments?: string;
  rejectionReason?: string;
}

// New interface for floors
export interface Floor {
  id: string;
  hostelId: string;
  floorNumber: number;
  floorName: string;
  totalRooms: number;
  description?: string;
  amenities: string[];
  createdDate: string;
  updatedDate: string;
}

// New interface for rooms
export interface Room {
  id: string;
  hostelId: string;
  floorId: string;
  roomNumber: string;
  roomType: 'single' | 'double' | 'triple' | 'quad' | 'dormitory';
  capacity: number;
  currentOccupancy: number;
  pricePerBed: number;
  amenities: string[];
  description?: string;
  images: string[];
  status: 'available' | 'occupied' | 'maintenance' | 'inactive';
  dimensions?: {
    length: number;
    width: number;
    area: number;
  };
  furnishing: string[];
  createdDate: string;
  updatedDate: string;
}

// New interface for account requests
export interface AccountRequest {
  id: string;
  // Personal Information
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  address: {
    street: string;
    city: string;
    state: string;
    pincode: string;
  };
  // Business Information
  businessName: string;
  businessType: 'individual' | 'partnership' | 'company' | 'llp';
  businessLicense: string;
  gstNumber?: string;
  panNumber: string;
  // Documents
  documents: {
    identityProof: string; // File path/URL
    addressProof: string; // File path/URL
    businessLicense: string; // File path/URL
    panCard: string; // File path/URL
    gstCertificate?: string; // File path/URL
  };
  // Request Details
  reasonForRequest: string;
  experienceInHospitality?: string;
  numberOfPropertiesPlanned: number;
  estimatedInvestment?: number;
  // Status and Tracking
  status: 'pending' | 'approved' | 'rejected';
  submittedDate: string;
  reviewedDate?: string;
  reviewedBy?: string;
  reviewComments?: string;
  rejectionReason?: string;
  // Generated Account Details (when approved)
  generatedUserId?: string;
  generatedUsername?: string;
  temporaryPassword?: string;
  accountCreatedDate?: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: 'superadmin' | 'owner' | 'employee' | 'member';
  hostelId?: string;
  avatar?: string;
  joinedDate: string;
  status: 'active' | 'inactive';
}

export interface Booking {
  id: string;
  userId: string;
  hostelId: string;
  bedNumber: string;
  checkIn: string;
  checkOut: string;
  amount: number;
  status: 'confirmed' | 'pending' | 'cancelled';
  paymentStatus: 'paid' | 'pending' | 'failed';
}

export interface Payment {
  id: string;
  bookingId: string;
  userId: string;
  amount: number;
  date: string;
  method: 'card' | 'upi' | 'bank_transfer';
  status: 'success' | 'pending' | 'failed';
  splitDetails: {
    hostelOwner: number;
    platform: number;
  };
}

export interface Complaint {
  id: string;
  userId: string;
  hostelId: string;
  title: string;
  description: string;
  category: 'maintenance' | 'cleanliness' | 'noise' | 'service' | 'other';
  status: 'open' | 'in_progress' | 'resolved';
  priority: 'low' | 'medium' | 'high';
  createdDate: string;
  resolvedDate?: string;
}

// Mock Data
export const mockHostels: Hostel[] = [
  {
    id: '1',
    name: 'Urban Stay Hostel',
    address: '123 College Road',
    city: 'Mumbai',
    state: 'Maharashtra',
    pincode: '400001',
    totalBeds: 100,
    availableBeds: 25,
    pricePerBed: 8000,
    amenities: ['WiFi', 'AC', 'Laundry', 'Mess', 'Security'],
    rating: 4.5,
    images: ['/placeholder.svg'],
    ownerId: '2',
    employees: ['3', '4'],
    status: 'active',
    approvalStatus: 'approved',
    createdDate: '2023-02-15',
    approvedDate: '2023-02-20'
  },
  {
    id: '2',
    name: 'Campus Lodge',
    address: '456 University Lane',
    city: 'Pune',
    state: 'Maharashtra',
    pincode: '411001',
    totalBeds: 80,
    availableBeds: 15,
    pricePerBed: 7500,
    amenities: ['WiFi', 'Gym', 'Mess', 'Study Room', 'Parking'],
    rating: 4.2,
    images: ['/placeholder.svg'],
    ownerId: '5',
    employees: ['6'],
    status: 'active',
    approvalStatus: 'approved',
    createdDate: '2023-03-01',
    approvedDate: '2023-03-05'
  },
  {
    id: '3',
    name: 'Elite Residency',
    address: '789 Tech Park',
    city: 'Bangalore',
    state: 'Karnataka',
    pincode: '560001',
    totalBeds: 150,
    availableBeds: 45,
    pricePerBed: 12000,
    amenities: ['WiFi', 'AC', 'Gym', 'Swimming Pool', 'Cafeteria', 'Security'],
    rating: 4.8,
    images: ['/placeholder.svg'],
    ownerId: '7',
    employees: ['8', '9', '10'],
    status: 'active',
    approvalStatus: 'approved',
    createdDate: '2023-03-10',
    approvedDate: '2023-03-15'
  }
];

// Mock data for hostel registrations
export const mockHostelRegistrations: HostelRegistration[] = [
  {
    id: 'reg-1',
    name: 'Green Valley Hostel',
    address: '456 Garden Street',
    city: 'Chennai',
    state: 'Tamil Nadu',
    pincode: '600001',
    description: 'A modern hostel with eco-friendly amenities and excellent connectivity to major colleges and IT parks.',
    totalBeds: 120,
    pricePerBed: 9500,
    amenities: ['WiFi', 'AC', 'Solar Power', 'Organic Mess', 'Library', 'Security'],
    images: ['/placeholder.svg', '/placeholder.svg'],
    ownerId: '13',
    ownerName: 'Arjun Reddy',
    ownerEmail: '<EMAIL>',
    ownerPhone: '+91 **********',
    contactPerson: 'Arjun Reddy',
    contactPhone: '+91 **********',
    contactEmail: '<EMAIL>',
    licenseNumber: 'TN-HST-2024-001',
    gstNumber: '33**********1Z5',
    bankAccountDetails: {
      accountNumber: '**********',
      ifscCode: 'HDFC0001234',
      bankName: 'HDFC Bank',
      accountHolderName: 'Arjun Reddy'
    },
    status: 'pending',
    submittedDate: '2024-01-15'
  },
  {
    id: 'reg-2',
    name: 'Tech Hub Residency',
    address: '789 Innovation Drive',
    city: 'Hyderabad',
    state: 'Telangana',
    pincode: '500001',
    description: 'Premium hostel designed for tech professionals and students with state-of-the-art facilities.',
    totalBeds: 200,
    pricePerBed: 11000,
    amenities: ['WiFi', 'AC', 'Coworking Space', 'Gym', 'Cafeteria', '24/7 Security', 'Parking'],
    images: ['/placeholder.svg', '/placeholder.svg', '/placeholder.svg'],
    ownerId: '14',
    ownerName: 'Priya Sharma',
    ownerEmail: '<EMAIL>',
    ownerPhone: '+91 **********',
    contactPerson: 'Priya Sharma',
    contactPhone: '+91 **********',
    contactEmail: '<EMAIL>',
    licenseNumber: 'TS-HST-2024-002',
    gstNumber: '36**********2L9',
    bankAccountDetails: {
      accountNumber: '**********',
      ifscCode: 'ICICI0005678',
      bankName: 'ICICI Bank',
      accountHolderName: 'Priya Sharma'
    },
    status: 'pending',
    submittedDate: '2024-01-20'
  },
  {
    id: 'reg-3',
    name: 'Budget Stay Inn',
    address: '321 Student Colony',
    city: 'Jaipur',
    state: 'Rajasthan',
    pincode: '302001',
    description: 'Affordable accommodation for students with basic amenities and good connectivity.',
    totalBeds: 60,
    pricePerBed: 6000,
    amenities: ['WiFi', 'Mess', 'Study Room', 'Security'],
    images: ['/placeholder.svg'],
    ownerId: '15',
    ownerName: 'Vikram Singh',
    ownerEmail: '<EMAIL>',
    ownerPhone: '+91 **********',
    contactPerson: 'Vikram Singh',
    contactPhone: '+91 **********',
    contactEmail: '<EMAIL>',
    licenseNumber: 'RJ-HST-2024-003',
    bankAccountDetails: {
      accountNumber: '**********',
      ifscCode: '**********',
      bankName: 'State Bank of India',
      accountHolderName: 'Vikram Singh'
    },
    status: 'rejected',
    submittedDate: '2024-01-10',
    reviewedDate: '2024-01-18',
    reviewedBy: '1',
    rejectionReason: 'Insufficient safety measures and documentation incomplete.'
  }
];

export const mockUsers: User[] = [
  {
    id: '1',
    name: 'Admin User',
    email: '<EMAIL>',
    phone: '+91 **********',
    role: 'superadmin',
    joinedDate: '2023-01-01',
    status: 'active'
  },
  {
    id: '2',
    name: 'Rajesh Kumar',
    email: '<EMAIL>',
    phone: '+91 **********',
    role: 'owner',
    hostelId: '1',
    joinedDate: '2023-02-15',
    status: 'active'
  },
  {
    id: '3',
    name: 'Priya Sharma',
    email: '<EMAIL>',
    phone: '+91 **********',
    role: 'employee',
    hostelId: '1',
    joinedDate: '2023-03-10',
    status: 'active'
  },
  {
    id: '4',
    name: 'Amit Singh',
    email: '<EMAIL>',
    phone: '+91 **********',
    role: 'employee',
    hostelId: '1',
    joinedDate: '2023-03-15',
    status: 'active'
  },
  {
    id: '11',
    name: 'Rohit Verma',
    email: '<EMAIL>',
    phone: '+91 9876543220',
    role: 'member',
    joinedDate: '2023-06-01',
    status: 'active'
  },
  {
    id: '12',
    name: 'Sneha Patel',
    email: '<EMAIL>',
    phone: '+91 9876543221',
    role: 'member',
    joinedDate: '2023-06-15',
    status: 'active'
  },
  {
    id: '13',
    name: 'Arjun Reddy',
    email: '<EMAIL>',
    phone: '+91 **********',
    role: 'owner',
    joinedDate: '2024-01-10',
    status: 'active'
  },
  {
    id: '14',
    name: 'Priya Sharma',
    email: '<EMAIL>',
    phone: '+91 **********',
    role: 'owner',
    joinedDate: '2024-01-15',
    status: 'active'
  },
  {
    id: '15',
    name: 'Vikram Singh',
    email: '<EMAIL>',
    phone: '+91 **********',
    role: 'owner',
    joinedDate: '2024-01-05',
    status: 'active'
  },
  {
    id: '16',
    name: 'Meera Patel',
    email: '<EMAIL>',
    phone: '+91 **********',
    role: 'owner',
    joinedDate: '2024-01-22',
    status: 'active'
  }
];

export const mockBookings: Booking[] = [
  {
    id: '1',
    userId: '11',
    hostelId: '1',
    bedNumber: 'A101',
    checkIn: '2024-01-01',
    checkOut: '2024-06-30',
    amount: 48000,
    status: 'confirmed',
    paymentStatus: 'paid'
  },
  {
    id: '2',
    userId: '12',
    hostelId: '3',
    bedNumber: 'B205',
    checkIn: '2024-02-01',
    checkOut: '2024-07-31',
    amount: 72000,
    status: 'confirmed',
    paymentStatus: 'paid'
  }
];

export const mockPayments: Payment[] = [
  {
    id: '1',
    bookingId: '1',
    userId: '11',
    amount: 48000,
    date: '2023-12-25',
    method: 'upi',
    status: 'success',
    splitDetails: {
      hostelOwner: 45600,
      platform: 2400
    }
  },
  {
    id: '2',
    bookingId: '2',
    userId: '12',
    amount: 72000,
    date: '2024-01-25',
    method: 'card',
    status: 'success',
    splitDetails: {
      hostelOwner: 68400,
      platform: 3600
    }
  }
];

export const mockComplaints: Complaint[] = [
  {
    id: '1',
    userId: '11',
    hostelId: '1',
    title: 'AC not working in room A101',
    description: 'The air conditioning unit has stopped working since yesterday. The room is getting very hot.',
    category: 'maintenance',
    status: 'in_progress',
    priority: 'high',
    createdDate: '2024-01-15'
  },
  {
    id: '2',
    userId: '12',
    hostelId: '3',
    title: 'Noisy neighbors',
    description: 'There is excessive noise from the room next door during late night hours.',
    category: 'noise',
    status: 'open',
    priority: 'medium',
    createdDate: '2024-01-20'
  }
];

// Mock data for floors
export const mockFloors: Floor[] = [
  {
    id: 'floor-1',
    hostelId: '1',
    floorNumber: 1,
    floorName: 'Ground Floor',
    totalRooms: 10,
    description: 'Ground floor with reception, common area, and rooms',
    amenities: ['Reception', 'Common Room', 'Dining Area'],
    createdDate: '2023-02-25',
    updatedDate: '2023-02-25'
  },
  {
    id: 'floor-2',
    hostelId: '1',
    floorNumber: 2,
    floorName: 'First Floor',
    totalRooms: 15,
    description: 'First floor with standard rooms',
    amenities: ['Study Room', 'Laundry Room'],
    createdDate: '2023-02-25',
    updatedDate: '2023-02-25'
  },
  {
    id: 'floor-3',
    hostelId: '2',
    floorNumber: 1,
    floorName: 'Ground Floor',
    totalRooms: 8,
    description: 'Ground floor with reception and premium rooms',
    amenities: ['Reception', 'Lounge', 'Cafeteria'],
    createdDate: '2023-03-10',
    updatedDate: '2023-03-10'
  }
];

// Mock data for rooms
export const mockRooms: Room[] = [
  {
    id: 'room-1',
    hostelId: '1',
    floorId: 'floor-1',
    roomNumber: 'G-101',
    roomType: 'double',
    capacity: 2,
    currentOccupancy: 1,
    pricePerBed: 8000,
    amenities: ['AC', 'Attached Bathroom', 'Study Table', 'Wardrobe'],
    description: 'Spacious double room with all basic amenities',
    images: ['/placeholder.svg'],
    status: 'available',
    dimensions: {
      length: 12,
      width: 10,
      area: 120
    },
    furnishing: ['Bed', 'Mattress', 'Study Table', 'Chair', 'Wardrobe', 'Bookshelf'],
    createdDate: '2023-02-26',
    updatedDate: '2023-02-26'
  },
  {
    id: 'room-2',
    hostelId: '1',
    floorId: 'floor-1',
    roomNumber: 'G-102',
    roomType: 'single',
    capacity: 1,
    currentOccupancy: 1,
    pricePerBed: 10000,
    amenities: ['AC', 'Attached Bathroom', 'Study Table', 'Wardrobe', 'Mini Fridge'],
    description: 'Premium single room with additional amenities',
    images: ['/placeholder.svg'],
    status: 'occupied',
    dimensions: {
      length: 10,
      width: 8,
      area: 80
    },
    furnishing: ['Bed', 'Mattress', 'Study Table', 'Chair', 'Wardrobe', 'Bookshelf', 'Sofa'],
    createdDate: '2023-02-26',
    updatedDate: '2023-02-26'
  },
  {
    id: 'room-3',
    hostelId: '1',
    floorId: 'floor-2',
    roomNumber: 'F-201',
    roomType: 'triple',
    capacity: 3,
    currentOccupancy: 2,
    pricePerBed: 7500,
    amenities: ['AC', 'Attached Bathroom', 'Study Tables', 'Wardrobes'],
    description: 'Triple sharing room with comfortable space',
    images: ['/placeholder.svg'],
    status: 'available',
    dimensions: {
      length: 16,
      width: 14,
      area: 224
    },
    furnishing: ['Beds', 'Mattresses', 'Study Tables', 'Chairs', 'Wardrobes'],
    createdDate: '2023-02-26',
    updatedDate: '2023-02-26'
  },
  {
    id: 'room-4',
    hostelId: '2',
    floorId: 'floor-3',
    roomNumber: 'G-101',
    roomType: 'double',
    capacity: 2,
    currentOccupancy: 0,
    pricePerBed: 7500,
    amenities: ['AC', 'Attached Bathroom', 'Study Table', 'Wardrobe'],
    description: 'Standard double room with all necessary amenities',
    images: ['/placeholder.svg'],
    status: 'available',
    dimensions: {
      length: 12,
      width: 10,
      area: 120
    },
    furnishing: ['Bed', 'Mattress', 'Study Table', 'Chair', 'Wardrobe'],
    createdDate: '2023-03-12',
    updatedDate: '2023-03-12'
  }
];

// Helper functions
export const getUserById = (id: string) => mockUsers.find(user => user.id === id);
export const getHostelById = (id: string) => mockHostels.find(hostel => hostel.id === id);
export const getBookingsByUserId = (userId: string) => mockBookings.filter(booking => booking.userId === userId);
export const getComplaintsByHostelId = (hostelId: string) => mockComplaints.filter(complaint => complaint.hostelId === hostelId);
export const getHostelRegistrationById = (id: string) => mockHostelRegistrations.find(reg => reg.id === id);
export const getHostelRegistrationsByOwnerId = (ownerId: string) => mockHostelRegistrations.filter(reg => reg.ownerId === ownerId);
export const getFloorsByHostelId = (hostelId: string) => mockFloors.filter(floor => floor.hostelId === hostelId);
export const getRoomsByFloorId = (floorId: string) => mockRooms.filter(room => room.floorId === floorId);
export const getRoomsByHostelId = (hostelId: string) => mockRooms.filter(room => room.hostelId === hostelId);

// Mock data for account requests
export const mockAccountRequests: AccountRequest[] = [
  {
    id: 'acc-req-1',
    firstName: 'Rahul',
    lastName: 'Sharma',
    email: '<EMAIL>',
    phone: '+91 **********',
    dateOfBirth: '1985-03-15',
    address: {
      street: '123 Business District, Sector 18',
      city: 'Gurgaon',
      state: 'Haryana',
      pincode: '122001'
    },
    businessName: 'Sharma Hospitality Services',
    businessType: 'company',
    businessLicense: 'HR-BL-2024-001',
    gstNumber: '06**********1Z5',
    panNumber: '**********',
    documents: {
      identityProof: '/documents/rahul-aadhar.pdf',
      addressProof: '/documents/rahul-address.pdf',
      businessLicense: '/documents/sharma-business-license.pdf',
      panCard: '/documents/rahul-pan.pdf',
      gstCertificate: '/documents/sharma-gst.pdf'
    },
    reasonForRequest: 'I want to expand my hospitality business by partnering with Room Buddy Hub to manage multiple hostel properties in the NCR region. I have 5 years of experience in the hospitality industry.',
    experienceInHospitality: '5 years in hotel and guest house management',
    numberOfPropertiesPlanned: 3,
    estimatedInvestment: 5000000,
    status: 'pending',
    submittedDate: '2024-01-25'
  },
  {
    id: 'acc-req-2',
    firstName: 'Meera',
    lastName: 'Patel',
    email: '<EMAIL>',
    phone: '+91 **********',
    dateOfBirth: '1990-07-22',
    address: {
      street: '456 Commercial Complex, Vastrapur',
      city: 'Ahmedabad',
      state: 'Gujarat',
      pincode: '380015'
    },
    businessName: 'Patel Properties',
    businessType: 'partnership',
    businessLicense: 'GJ-BL-2024-002',
    gstNumber: '24**********2L9',
    panNumber: '**********',
    documents: {
      identityProof: '/documents/meera-aadhar.pdf',
      addressProof: '/documents/meera-address.pdf',
      businessLicense: '/documents/patel-business-license.pdf',
      panCard: '/documents/meera-pan.pdf',
      gstCertificate: '/documents/patel-gst.pdf'
    },
    reasonForRequest: 'Looking to convert my existing rental properties into managed hostels for students and working professionals. I believe Room Buddy Hub platform will help me reach more customers.',
    experienceInHospitality: '2 years in property rental management',
    numberOfPropertiesPlanned: 2,
    estimatedInvestment: 2500000,
    status: 'approved',
    submittedDate: '2024-01-20',
    reviewedDate: '2024-01-22',
    reviewedBy: '1',
    reviewComments: 'Good business plan and proper documentation. Approved for account creation.',
    generatedUserId: '16',
    generatedUsername: 'meera.patel',
    temporaryPassword: 'TempPass123!',
    accountCreatedDate: '2024-01-22'
  },
  {
    id: 'acc-req-3',
    firstName: 'Karthik',
    lastName: 'Reddy',
    email: '<EMAIL>',
    phone: '+91 **********',
    dateOfBirth: '1988-11-10',
    address: {
      street: '789 IT Corridor, Madhapur',
      city: 'Hyderabad',
      state: 'Telangana',
      pincode: '500081'
    },
    businessName: 'Reddy Accommodations',
    businessType: 'individual',
    businessLicense: 'TS-BL-2024-003',
    panNumber: '**********',
    documents: {
      identityProof: '/documents/karthik-aadhar.pdf',
      addressProof: '/documents/karthik-address.pdf',
      businessLicense: '/documents/reddy-business-license.pdf',
      panCard: '/documents/karthik-pan.pdf'
    },
    reasonForRequest: 'I am a software engineer looking to start a side business in hospitality. I want to create premium hostels for IT professionals in Hyderabad.',
    experienceInHospitality: 'No prior experience, but have researched the market extensively',
    numberOfPropertiesPlanned: 1,
    estimatedInvestment: 1500000,
    status: 'rejected',
    submittedDate: '2024-01-18',
    reviewedDate: '2024-01-21',
    reviewedBy: '1',
    rejectionReason: 'Insufficient experience in hospitality industry and incomplete business documentation.',
    reviewComments: 'Please gain some experience in hospitality management and reapply with a more detailed business plan.'
  }
];

// Helper functions for account requests
export const getAccountRequestById = (id: string) => mockAccountRequests.find(req => req.id === id);
export const getAccountRequestsByStatus = (status: AccountRequest['status']) => mockAccountRequests.filter(req => req.status === status);
export const getPendingAccountRequests = () => mockAccountRequests.filter(req => req.status === 'pending');
export const getApprovedAccountRequests = () => mockAccountRequests.filter(req => req.status === 'approved');
export const getRejectedAccountRequests = () => mockAccountRequests.filter(req => req.status === 'rejected');