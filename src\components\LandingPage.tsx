import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Building2,
  Users,
  Shield,
  CreditCard,
  MessageSquare,
  Star,
  ArrowRight,
  CheckCircle,
  TrendingUp,
  Cog
} from 'lucide-react';
import { Link } from 'react-router-dom';
import heroImage from '@/assets/hero-hostel.jpg';

interface LandingPageProps {
  onPanelChange?: (panel: 'superadmin' | 'owner' | 'employee' | 'user') => void;
}

export const LandingPage = ({ onPanelChange }: LandingPageProps) => {
  const features = [
    {
      icon: Building2,
      title: 'Multi-Platform Management',
      description: 'Comprehensive hostel management across web and mobile platforms with role-based access.'
    },
    {
      icon: Users,
      title: 'Role-Based Access Control',
      description: 'Secure access management for Super Admins, Hostel Owners, Employees, and Members.'
    },
    {
      icon: Shield,
      title: 'Secure & Reliable',
      description: 'Enterprise-grade security with robust data protection and reliable uptime.'
    },
    {
      icon: CreditCard,
      title: 'Payment Processing',
      description: 'Integrated payment system with split billing and multiple payment methods.'
    },
    {
      icon: MessageSquare,
      title: 'Communication Hub',
      description: 'Built-in messaging system with WhatsApp integration and push notifications.'
    },
    {
      icon: Star,
      title: 'Quality Management',
      description: 'Complaint management system with priority handling and resolution tracking.'
    }
  ];

  const stats = [
    { label: 'Active Hostels', value: '500+' },
    { label: 'Happy Members', value: '10K+' },
    { label: 'Cities Covered', value: '50+' },
    { label: 'Uptime', value: '99.9%' }
  ];

  return (
    <div className="min-h-screen">
      {/* Navigation Header */}
      <header className="absolute top-0 left-0 right-0 z-50 bg-white/10 backdrop-blur-md border-b border-white/20">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-2">
              <Building2 className="h-8 w-8 text-white" />
              <span className="text-xl font-bold text-white">Room Buddy Hub</span>
            </div>
            <div className="flex items-center space-x-4">
              <Link to="/login">
                <Button variant="ghost" className="text-white hover:bg-white/20">
                  Sign In
                </Button>
              </Link>
              <Link to="/account-request">
                <Button className="bg-white text-primary hover:bg-white/90 shadow-lg">
                  Become a Partner
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-20 lg:py-32 bg-gradient-hero overflow-hidden">
        <div className="absolute inset-0 bg-black/20" />
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20"
          style={{ backgroundImage: `url(${heroImage})` }}
        />
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center text-white">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Modern Hostel Management
              <span className="block bg-gradient-accent bg-clip-text text-transparent">
                Made Simple
              </span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-white/90 max-w-3xl mx-auto">
              Comprehensive multi-platform solution for hostel management with role-based access, 
              payment processing, and seamless communication.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/login">
                <Button
                  size="lg"
                  className="bg-white text-primary hover:bg-white/90 shadow-glow"
                >
                  Find Hostels
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link to="/account-request">
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-primary"
                >
                  Become a Partner
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
                  {stat.value}
                </div>
                <div className="text-muted-foreground">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Everything You Need to Manage Hostels
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Our comprehensive platform provides all the tools you need for efficient hostel management.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <Card key={index} className="group hover:shadow-elevation transition-all duration-300">
                  <CardHeader>
                    <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center mb-4 group-hover:shadow-glow transition-all duration-300">
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">{feature.description}</p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-16 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div className="space-y-2">
              <div className="text-3xl md:text-4xl font-bold text-blue-600">500+</div>
              <div className="text-sm text-gray-600 font-medium">Partner Hostels</div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl md:text-4xl font-bold text-green-600">50K+</div>
              <div className="text-sm text-gray-600 font-medium">Happy Tenants</div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl md:text-4xl font-bold text-purple-600">25+</div>
              <div className="text-sm text-gray-600 font-medium">Cities Covered</div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl md:text-4xl font-bold text-orange-600">95%</div>
              <div className="text-sm text-gray-600 font-medium">Owner Satisfaction</div>
            </div>
          </div>
        </div>
      </section>

      {/* Partner with Us Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">
                Partner with Room Buddy Hub
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Join our network of successful hostel owners and grow your hospitality business with our comprehensive platform
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-12 items-center">
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Users className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Reach More Customers</h3>
                    <p className="text-gray-600">Connect with thousands of students and professionals looking for quality accommodation across India.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Shield className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Secure & Reliable</h3>
                    <p className="text-gray-600">Safe payment processing, verified users, and comprehensive insurance coverage for your peace of mind.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <TrendingUp className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Grow Your Revenue</h3>
                    <p className="text-gray-600">Advanced analytics, dynamic pricing tools, and marketing support to maximize your occupancy and revenue.</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Cog className="h-6 w-6 text-orange-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Easy Management</h3>
                    <p className="text-gray-600">Streamlined booking system, automated operations, and 24/7 support to simplify your hostel management.</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-2xl shadow-xl p-8">
                <div className="text-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Building2 className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">Ready to Get Started?</h3>
                  <p className="text-gray-600">Join hundreds of successful hostel owners already using our platform</p>
                </div>

                <div className="space-y-4">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">Application Process</span>
                      <span className="text-sm text-blue-600">3-5 days</span>
                    </div>
                    <div className="mt-2 text-xs text-gray-500">Quick review and approval</div>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">Setup Support</span>
                      <span className="text-sm text-green-600">Free</span>
                    </div>
                    <div className="mt-2 text-xs text-gray-500">Complete onboarding assistance</div>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">Commission</span>
                      <span className="text-sm text-purple-600">Competitive</span>
                    </div>
                    <div className="mt-2 text-xs text-gray-500">Industry-leading rates</div>
                  </div>
                </div>

                <div className="mt-8 space-y-3">
                  <Link to="/account-request" className="block">
                    <Button className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg">
                      Apply for Partnership
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                  <p className="text-xs text-gray-500 text-center">
                    Already have an account? <Link to="/login" className="text-blue-600 hover:underline">Sign in here</Link>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Success Stories Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Success Stories from Our Partners
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Hear from hostel owners who have transformed their business with Room Buddy Hub
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-gray-50 rounded-xl p-6 shadow-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                  <span className="text-blue-600 font-bold text-lg">RS</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">Rajesh Sharma</h4>
                  <p className="text-sm text-gray-600">Owner, Urban Stay Hostel, Delhi</p>
                </div>
              </div>
              <div className="flex mb-3">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-700 text-sm leading-relaxed">
                "Room Buddy Hub increased our occupancy by 40% in just 3 months. The platform is incredibly easy to use,
                and their support team is always there when we need help. Best decision for our business!"
              </p>
            </div>

            <div className="bg-gray-50 rounded-xl p-6 shadow-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                  <span className="text-green-600 font-bold text-lg">MP</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">Meera Patel</h4>
                  <p className="text-sm text-gray-600">Owner, Patel Properties, Ahmedabad</p>
                </div>
              </div>
              <div className="flex mb-3">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-700 text-sm leading-relaxed">
                "The automated booking system and payment processing have saved us countless hours.
                We can now focus on providing better services to our guests instead of paperwork."
              </p>
            </div>

            <div className="bg-gray-50 rounded-xl p-6 shadow-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                  <span className="text-purple-600 font-bold text-lg">AK</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">Arjun Kumar</h4>
                  <p className="text-sm text-gray-600">Owner, Tech Hub Residency, Bangalore</p>
                </div>
              </div>
              <div className="flex mb-3">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-700 text-sm leading-relaxed">
                "Revenue increased by 60% after joining Room Buddy Hub. The analytics dashboard helps us make
                data-driven decisions, and the marketing support brings in quality tenants."
              </p>
            </div>
          </div>

          <div className="text-center mt-12">
            <Link to="/account-request">
              <Button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-3">
                Join These Successful Owners
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Panels Section */}
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Access Your Dashboard
            </h2>
            <p className="text-xl text-muted-foreground">
              Choose your role to access the appropriate management panel
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="hover:shadow-elevation transition-all duration-300 group">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4 group-hover:shadow-glow transition-all duration-300">
                  <Users className="h-8 w-8 text-white" />
                </div>
                <CardTitle>Super Admin</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-muted-foreground mb-4">
                  System-wide management and analytics
                </p>
                <Link to="/login">
                  <Button variant="outline" className="w-full">
                    Access Panel
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="hover:shadow-elevation transition-all duration-300 group">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-4 group-hover:shadow-glow transition-all duration-300">
                  <Building2 className="h-8 w-8 text-white" />
                </div>
                <CardTitle>Hostel Provider</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-muted-foreground mb-4">
                  Manage your hostels and employees
                </p>
                <Link to="/login">
                  <Button variant="outline" className="w-full">
                    Access Panel
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="hover:shadow-elevation transition-all duration-300 group">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4 group-hover:shadow-glow transition-all duration-300">
                  <Users className="h-8 w-8 text-white" />
                </div>
                <CardTitle>Employee</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-muted-foreground mb-4">
                  Daily operations and member management
                </p>
                <Link to="/login">
                  <Button variant="outline" className="w-full">
                    Access Panel
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card className="hover:shadow-elevation transition-all duration-300 group">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-4 group-hover:shadow-glow transition-all duration-300">
                  <Users className="h-8 w-8 text-white" />
                </div>
                <CardTitle>User Panel</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-muted-foreground mb-4">
                  Find and book hostel accommodations
                </p>
                <Link to="/login">
                  <Button variant="outline" className="w-full">
                    Access Panel
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Call to Action Footer */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Ready to Transform Your Hostel Business?
            </h2>
            <p className="text-xl mb-8 text-white/90 max-w-2xl mx-auto">
              Join thousands of successful hostel owners who have already partnered with Room Buddy Hub.
              Start your journey today and unlock your property's full potential.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/account-request">
                <Button
                  size="lg"
                  className="bg-white text-primary hover:bg-white/90 shadow-xl"
                >
                  Apply for Partnership
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link to="/login">
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-primary"
                >
                  Existing Partner? Sign In
                </Button>
              </Link>
            </div>
            <p className="text-sm text-white/70 mt-6">
              Questions? Contact us at <a href="mailto:<EMAIL>" className="underline hover:text-white"><EMAIL></a> or call +91 1800-123-4567
            </p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8 mb-8">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Building2 className="h-6 w-6 text-blue-400" />
                <span className="text-lg font-semibold">Room Buddy Hub</span>
              </div>
              <p className="text-gray-400 text-sm">
                India's leading hostel management platform connecting property owners with quality tenants.
              </p>
            </div>

            <div>
              <h3 className="font-semibold mb-4">For Owners</h3>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><Link to="/account-request" className="hover:text-white transition-colors">Become a Partner</Link></li>
                <li><Link to="/login" className="hover:text-white transition-colors">Owner Login</Link></li>
                <li><a href="#" className="hover:text-white transition-colors">Success Stories</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Pricing</a></li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4">For Tenants</h3>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><Link to="/login" className="hover:text-white transition-colors">Find Hostels</Link></li>
                <li><a href="#" className="hover:text-white transition-colors">How it Works</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Safety & Security</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Support</a></li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">About Us</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Privacy Policy</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Terms of Service</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8">
            <div className="flex flex-col md:flex-row items-center justify-between">
              <div className="text-gray-400 text-sm mb-4 md:mb-0">
                © 2024 Room Buddy Hub. All rights reserved.
              </div>
              <div className="flex space-x-6">
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <span className="sr-only">Facebook</span>
                  📘
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <span className="sr-only">Twitter</span>
                  🐦
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <span className="sr-only">Instagram</span>
                  📷
                </a>
                <a href="#" className="text-gray-400 hover:text-white transition-colors">
                  <span className="sr-only">LinkedIn</span>
                  💼
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};