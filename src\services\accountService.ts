// Account Service - API layer for account request and user management operations

import {
  AccountRequest,
  User,
  mockAccountRequests,
  mockUsers
} from '@/data/mockData';
import { NotificationService, StatusTracker } from '@/utils/notificationService';
import { EmailService } from './emailService';

// Base API response interface
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Account credentials interface
interface AccountCredentials {
  username: string;
  temporaryPassword: string;
  userId: string;
}

// Account Request Service
export class AccountRequestService {
  // Submit new account request
  static async submitAccountRequest(
    requestData: Omit<AccountRequest, 'id' | 'status' | 'submittedDate' | 'documents'>,
    documents: File[]
  ): Promise<ApiResponse<AccountRequest>> {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // In a real app, files would be uploaded to cloud storage
      const documentPaths = {
        identityProof: `/documents/${requestData.firstName.toLowerCase()}-identity.pdf`,
        addressProof: `/documents/${requestData.firstName.toLowerCase()}-address.pdf`,
        businessLicense: `/documents/${requestData.businessName.toLowerCase().replace(/\s+/g, '-')}-license.pdf`,
        panCard: `/documents/${requestData.firstName.toLowerCase()}-pan.pdf`,
        gstCertificate: requestData.gstNumber ? `/documents/${requestData.businessName.toLowerCase().replace(/\s+/g, '-')}-gst.pdf` : undefined
      };

      const newAccountRequest: AccountRequest = {
        ...requestData,
        id: `acc-req-${Date.now()}`,
        documents: documentPaths,
        status: 'pending',
        submittedDate: new Date().toISOString().split('T')[0],
      };

      mockAccountRequests.push(newAccountRequest);

      // Notify admin of new account request
      NotificationService.notifyAdminOfNewAccountRequest(
        newAccountRequest.id,
        `${requestData.firstName} ${requestData.lastName}`,
        requestData.businessName
      );

      return {
        success: true,
        data: newAccountRequest,
        message: 'Account request submitted successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to submit account request'
      };
    }
  }

  // Get all account requests (admin only)
  static async getAllAccountRequests(): Promise<ApiResponse<AccountRequest[]>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      return {
        success: true,
        data: mockAccountRequests
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to fetch account requests'
      };
    }
  }

  // Get pending account requests (admin only)
  static async getPendingAccountRequests(): Promise<ApiResponse<AccountRequest[]>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));

      const pendingRequests = mockAccountRequests.filter(req => req.status === 'pending');
      
      return {
        success: true,
        data: pendingRequests
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to fetch pending account requests'
      };
    }
  }

  // Approve account request and create user account (admin only)
  static async approveAccountRequest(
    requestId: string,
    adminId: string,
    comments?: string
  ): Promise<ApiResponse<{ request: AccountRequest; credentials: AccountCredentials }>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const request = mockAccountRequests.find(req => req.id === requestId);
      if (!request) {
        return {
          success: false,
          error: 'Account request not found'
        };
      }

      if (request.status !== 'pending') {
        return {
          success: false,
          error: 'Account request has already been processed'
        };
      }

      // Generate user account credentials
      const credentials = UserAccountService.generateAccountCredentials(
        request.firstName,
        request.lastName,
        request.email
      );

      // Create user account
      const userCreationResult = await UserAccountService.createUserAccount({
        name: `${request.firstName} ${request.lastName}`,
        email: request.email,
        phone: request.phone,
        role: 'owner',
        username: credentials.username,
        temporaryPassword: credentials.temporaryPassword
      });

      if (!userCreationResult.success) {
        return {
          success: false,
          error: 'Failed to create user account'
        };
      }

      // Update request status
      const oldStatus = request.status;
      request.status = 'approved';
      request.reviewedDate = new Date().toISOString().split('T')[0];
      request.reviewedBy = adminId;
      request.reviewComments = comments;
      request.generatedUserId = userCreationResult.data!.id;
      request.generatedUsername = credentials.username;
      request.temporaryPassword = credentials.temporaryPassword;
      request.accountCreatedDate = new Date().toISOString().split('T')[0];

      // Track status change
      StatusTracker.trackRegistrationStatusChange(
        requestId,
        oldStatus,
        'approved',
        adminId,
        comments
      );

      // Notify applicant of approval
      NotificationService.notifyApplicantOfAccountApproval(
        userCreationResult.data!.id,
        `${request.firstName} ${request.lastName}`,
        request.businessName,
        credentials.username
      );

      // Send approval email with login credentials
      try {
        await EmailService.sendAccountApprovalEmail(request.email, {
          applicantName: `${request.firstName} ${request.lastName}`,
          businessName: request.businessName,
          username: credentials.username,
          temporaryPassword: credentials.temporaryPassword,
          loginUrl: `${window.location.origin}/login`
        });
      } catch (emailError) {
        console.error('Failed to send approval email:', emailError);
        // Don't fail the approval process if email fails
      }

      return {
        success: true,
        data: { request, credentials },
        message: 'Account request approved and user account created successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to approve account request'
      };
    }
  }

  // Reject account request (admin only)
  static async rejectAccountRequest(
    requestId: string,
    adminId: string,
    rejectionReason: string,
    comments?: string
  ): Promise<ApiResponse<AccountRequest>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const request = mockAccountRequests.find(req => req.id === requestId);
      if (!request) {
        return {
          success: false,
          error: 'Account request not found'
        };
      }

      if (request.status !== 'pending') {
        return {
          success: false,
          error: 'Account request has already been processed'
        };
      }

      // Update request status
      const oldStatus = request.status;
      request.status = 'rejected';
      request.reviewedDate = new Date().toISOString().split('T')[0];
      request.reviewedBy = adminId;
      request.rejectionReason = rejectionReason;
      request.reviewComments = comments;

      // Track status change
      StatusTracker.trackRegistrationStatusChange(
        requestId,
        oldStatus,
        'rejected',
        adminId,
        rejectionReason
      );

      // Send rejection email to applicant
      try {
        await EmailService.sendAccountRejectionEmail(request.email, {
          applicantName: `${request.firstName} ${request.lastName}`,
          businessName: request.businessName,
          rejectionReason,
          reapplyUrl: `${window.location.origin}/account-request`
        });
      } catch (emailError) {
        console.error('Failed to send rejection email:', emailError);
        // Don't fail the rejection process if email fails
      }

      return {
        success: true,
        data: request,
        message: 'Account request rejected'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to reject account request'
      };
    }
  }
}

// User Account Management Service
export class UserAccountService {
  // Generate secure account credentials
  static generateAccountCredentials(
    firstName: string,
    lastName: string,
    email: string
  ): AccountCredentials {
    // Generate username
    const baseUsername = `${firstName.toLowerCase()}.${lastName.toLowerCase()}`;
    let username = baseUsername;
    let counter = 1;

    // Ensure username is unique
    while (mockUsers.some(user => user.email === email)) {
      username = `${baseUsername}${counter}`;
      counter++;
    }

    // Generate secure temporary password
    const temporaryPassword = this.generateSecurePassword();

    return {
      username,
      temporaryPassword,
      userId: `${Date.now()}`
    };
  }

  // Generate secure password
  static generateSecurePassword(): string {
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '**********';
    const symbols = '!@#$%^&*';
    
    const allChars = uppercase + lowercase + numbers + symbols;
    
    let password = '';
    
    // Ensure at least one character from each category
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += symbols[Math.floor(Math.random() * symbols.length)];
    
    // Fill the rest randomly
    for (let i = 4; i < 12; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }
    
    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }

  // Create new user account
  static async createUserAccount(userData: {
    name: string;
    email: string;
    phone: string;
    role: 'owner';
    username: string;
    temporaryPassword: string;
  }): Promise<ApiResponse<User>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));

      // Check if user with email already exists
      const existingUser = mockUsers.find(user => user.email === userData.email);
      if (existingUser) {
        return {
          success: false,
          error: 'User with this email already exists'
        };
      }

      const newUser: User = {
        id: `${Date.now()}`,
        name: userData.name,
        email: userData.email,
        phone: userData.phone,
        role: userData.role,
        joinedDate: new Date().toISOString().split('T')[0],
        status: 'active'
      };

      mockUsers.push(newUser);

      // In a real app, you would also store the username and hashed password
      // in a separate authentication table

      return {
        success: true,
        data: newUser,
        message: 'User account created successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to create user account'
      };
    }
  }

  // Get user by ID
  static async getUserById(userId: string): Promise<ApiResponse<User>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));

      const user = mockUsers.find(u => u.id === userId);
      if (!user) {
        return {
          success: false,
          error: 'User not found'
        };
      }

      return {
        success: true,
        data: user
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to fetch user'
      };
    }
  }

  // Update user status
  static async updateUserStatus(
    userId: string,
    status: 'active' | 'inactive',
    updatedBy: string
  ): Promise<ApiResponse<User>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));

      const user = mockUsers.find(u => u.id === userId);
      if (!user) {
        return {
          success: false,
          error: 'User not found'
        };
      }

      user.status = status;

      return {
        success: true,
        data: user,
        message: `User status updated to ${status}`
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to update user status'
      };
    }
  }

  // Get all users (admin only)
  static async getAllUsers(): Promise<ApiResponse<User[]>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      return {
        success: true,
        data: mockUsers
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to fetch users'
      };
    }
  }

  // Get users by role
  static async getUsersByRole(role: User['role']): Promise<ApiResponse<User[]>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));

      const users = mockUsers.filter(user => user.role === role);
      
      return {
        success: true,
        data: users
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to fetch users by role'
      };
    }
  }
}
