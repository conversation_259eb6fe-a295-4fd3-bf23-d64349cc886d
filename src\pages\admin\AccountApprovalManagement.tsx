import React, { useState } from 'react';
import { 
  Users, 
  Eye, 
  Check, 
  X, 
  Clock, 
  Search,
  Filter,
  User,
  Building2,
  FileText,
  Phone,
  Mail,
  MapPin,
  CreditCard
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { 
  AccountRequest, 
  mockAccountRequests,
  mockUsers
} from '@/data/mockData';

export const AccountApprovalManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all');
  const [selectedRequest, setSelectedRequest] = useState<AccountRequest | null>(null);
  const [isReviewDialogOpen, setIsReviewDialogOpen] = useState(false);
  const [reviewComments, setReviewComments] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  // Filter account requests based on search and status
  const filteredRequests = mockAccountRequests.filter(request => {
    const matchesSearch = request.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.businessName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || request.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleApprove = async (request: AccountRequest) => {
    setIsProcessing(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update request status
      request.status = 'approved';
      request.reviewedDate = new Date().toISOString().split('T')[0];
      request.reviewedBy = '1'; // Admin user ID
      request.reviewComments = reviewComments;

      // Generate user account details
      const username = `${request.firstName.toLowerCase()}.${request.lastName.toLowerCase()}`;
      const temporaryPassword = generateTemporaryPassword();
      const newUserId = `${Date.now()}`;

      request.generatedUserId = newUserId;
      request.generatedUsername = username;
      request.temporaryPassword = temporaryPassword;
      request.accountCreatedDate = new Date().toISOString().split('T')[0];

      // Create new user account
      const newUser = {
        id: newUserId,
        name: `${request.firstName} ${request.lastName}`,
        email: request.email,
        phone: request.phone,
        role: 'owner' as const,
        joinedDate: new Date().toISOString().split('T')[0],
        status: 'active' as const
      };

      mockUsers.push(newUser);

      toast({
        title: 'Account Request Approved',
        description: `Account for ${request.firstName} ${request.lastName} has been approved and created.`,
      });

      setIsReviewDialogOpen(false);
      setReviewComments('');
      setSelectedRequest(null);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to approve account request. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleReject = async (request: AccountRequest) => {
    if (!rejectionReason.trim()) {
      toast({
        title: 'Rejection Reason Required',
        description: 'Please provide a reason for rejection.',
        variant: 'destructive',
      });
      return;
    }

    setIsProcessing(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update request status
      request.status = 'rejected';
      request.reviewedDate = new Date().toISOString().split('T')[0];
      request.reviewedBy = '1'; // Admin user ID
      request.rejectionReason = rejectionReason;
      request.reviewComments = reviewComments;

      toast({
        title: 'Account Request Rejected',
        description: `Account request for ${request.firstName} ${request.lastName} has been rejected.`,
      });

      setIsReviewDialogOpen(false);
      setReviewComments('');
      setRejectionReason('');
      setSelectedRequest(null);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to reject account request. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const generateTemporaryPassword = (): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  };

  const getStatusIcon = (status: AccountRequest['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'approved':
        return <Check className="h-4 w-4" />;
      case 'rejected':
        return <X className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: AccountRequest['status']) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
    }
  };

  const pendingCount = mockAccountRequests.filter(r => r.status === 'pending').length;
  const approvedCount = mockAccountRequests.filter(r => r.status === 'approved').length;
  const rejectedCount = mockAccountRequests.filter(r => r.status === 'rejected').length;

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Account Request Management</h1>
        <p className="text-muted-foreground">
          Review and manage hostel owner account requests
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Review</p>
                <p className="text-2xl font-bold text-yellow-600">{pendingCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Check className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Approved</p>
                <p className="text-2xl font-bold text-green-600">{approvedCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <X className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Rejected</p>
                <p className="text-2xl font-bold text-red-600">{rejectedCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Requests</p>
                <p className="text-2xl font-bold text-blue-600">{mockAccountRequests.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search by name, email, or business name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="w-full md:w-48">
              <Select value={statusFilter} onValueChange={(value: any) => setStatusFilter(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Account Requests Table */}
      <Card>
        <CardHeader>
          <CardTitle>Account Requests</CardTitle>
          <CardDescription>
            Review account requests and take action
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Applicant</TableHead>
                  <TableHead>Business</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Submitted</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRequests.map((request) => (
                  <TableRow key={request.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <p className="font-medium">{request.firstName} {request.lastName}</p>
                        <p className="text-sm text-muted-foreground">{request.email}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <p className="font-medium">{request.businessName}</p>
                        <p className="text-sm text-muted-foreground capitalize">{request.businessType}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <p className="text-sm">{request.phone}</p>
                        <p className="text-sm text-muted-foreground">{request.address.city}, {request.address.state}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <p className="text-sm">
                        {new Date(request.submittedDate).toLocaleDateString()}
                      </p>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant="outline" 
                        className={`flex items-center gap-1 w-fit ${getStatusColor(request.status)}`}
                      >
                        {getStatusIcon(request.status)}
                        {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => setSelectedRequest(request)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            Review
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                          <DialogHeader>
                            <DialogTitle>Review Account Request</DialogTitle>
                            <DialogDescription>
                              Review the details and approve or reject this account request
                            </DialogDescription>
                          </DialogHeader>
                          
                          {selectedRequest && (
                            <AccountRequestReviewDialog 
                              request={selectedRequest}
                              reviewComments={reviewComments}
                              setReviewComments={setReviewComments}
                              rejectionReason={rejectionReason}
                              setRejectionReason={setRejectionReason}
                              isProcessing={isProcessing}
                              handleApprove={() => handleApprove(selectedRequest)}
                              handleReject={() => handleReject(selectedRequest)}
                            />
                          )}
                        </DialogContent>
                      </Dialog>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredRequests.length === 0 && (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No account requests found</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

// Account Request Review Dialog Component
interface AccountRequestReviewDialogProps {
  request: AccountRequest;
  reviewComments: string;
  setReviewComments: (comments: string) => void;
  rejectionReason: string;
  setRejectionReason: (reason: string) => void;
  isProcessing: boolean;
  handleApprove: () => void;
  handleReject: () => void;
}

const AccountRequestReviewDialog: React.FC<AccountRequestReviewDialogProps> = ({
  request,
  reviewComments,
  setReviewComments,
  rejectionReason,
  setRejectionReason,
  isProcessing,
  handleApprove,
  handleReject,
}) => {
  return (
    <div className="space-y-6">
      {/* Personal Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Personal Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <Label className="text-sm font-medium">Full Name</Label>
              <p className="text-sm">{request.firstName} {request.lastName}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Email</Label>
              <p className="text-sm">{request.email}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Phone</Label>
              <p className="text-sm">{request.phone}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Date of Birth</Label>
              <p className="text-sm">{new Date(request.dateOfBirth).toLocaleDateString()}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">PAN Number</Label>
              <p className="text-sm">{request.panNumber}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Address Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <Label className="text-sm font-medium">Street Address</Label>
              <p className="text-sm">{request.address.street}</p>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">City</Label>
                <p className="text-sm">{request.address.city}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">State</Label>
                <p className="text-sm">{request.address.state}</p>
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium">Pincode</Label>
              <p className="text-sm">{request.address.pincode}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Business Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Business Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <Label className="text-sm font-medium">Business Name</Label>
              <p className="text-sm">{request.businessName}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Business Type</Label>
              <p className="text-sm capitalize">{request.businessType}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Business License</Label>
              <p className="text-sm">{request.businessLicense}</p>
            </div>
            {request.gstNumber && (
              <div>
                <Label className="text-sm font-medium">GST Number</Label>
                <p className="text-sm">{request.gstNumber}</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Request Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <Label className="text-sm font-medium">Properties Planned</Label>
              <p className="text-sm">{request.numberOfPropertiesPlanned}</p>
            </div>
            {request.estimatedInvestment && (
              <div>
                <Label className="text-sm font-medium">Estimated Investment</Label>
                <p className="text-sm">₹{request.estimatedInvestment.toLocaleString()}</p>
              </div>
            )}
            {request.experienceInHospitality && (
              <div>
                <Label className="text-sm font-medium">Experience</Label>
                <p className="text-sm">{request.experienceInHospitality}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Reason for Request */}
      <Card>
        <CardHeader>
          <CardTitle>Reason for Request</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">{request.reasonForRequest}</p>
        </CardContent>
      </Card>

      {/* Documents */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Submitted Documents
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium">Identity Proof</Label>
              <p className="text-sm text-blue-600 hover:underline cursor-pointer">
                {request.documents.identityProof}
              </p>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium">Address Proof</Label>
              <p className="text-sm text-blue-600 hover:underline cursor-pointer">
                {request.documents.addressProof}
              </p>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium">Business License</Label>
              <p className="text-sm text-blue-600 hover:underline cursor-pointer">
                {request.documents.businessLicense}
              </p>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium">PAN Card</Label>
              <p className="text-sm text-blue-600 hover:underline cursor-pointer">
                {request.documents.panCard}
              </p>
            </div>
            {request.documents.gstCertificate && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">GST Certificate</Label>
                <p className="text-sm text-blue-600 hover:underline cursor-pointer">
                  {request.documents.gstCertificate}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Review Section */}
      <Card>
        <CardHeader>
          <CardTitle>Review & Decision</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="reviewComments">Review Comments (Optional)</Label>
            <Textarea
              id="reviewComments"
              placeholder="Add any comments about this request..."
              value={reviewComments}
              onChange={(e) => setReviewComments(e.target.value)}
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="rejectionReason">Rejection Reason (Required for rejection)</Label>
            <Textarea
              id="rejectionReason"
              placeholder="Provide detailed reason for rejection..."
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              rows={3}
            />
          </div>

          {request.status === 'pending' && (
            <div className="flex gap-3 pt-4">
              <Button
                onClick={handleApprove}
                disabled={isProcessing}
                className="flex-1"
              >
                <Check className="h-4 w-4 mr-2" />
                Approve & Create Account
              </Button>
              <Button
                variant="destructive"
                onClick={handleReject}
                disabled={isProcessing}
                className="flex-1"
              >
                <X className="h-4 w-4 mr-2" />
                Reject Request
              </Button>
            </div>
          )}

          {request.status !== 'pending' && (
            <Alert>
              <AlertDescription>
                This request has already been {request.status}.
                {request.reviewedDate && ` Reviewed on ${new Date(request.reviewedDate).toLocaleDateString()}.`}
                {request.status === 'approved' && request.generatedUsername && (
                  <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded">
                    <p className="text-sm font-medium text-green-800">Account Created:</p>
                    <p className="text-sm text-green-700">Username: {request.generatedUsername}</p>
                    <p className="text-sm text-green-700">Temporary Password: {request.temporaryPassword}</p>
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
