// Notification Service for Hostel Management System

export interface Notification {
  id: string;
  userId: string;
  type: 'hostel_registration' | 'approval_status' | 'floor_room' | 'booking' | 'system' | 'account_request';
  title: string;
  message: string;
  status: 'unread' | 'read';
  priority: 'low' | 'medium' | 'high';
  createdAt: string;
  actionUrl?: string;
  metadata?: Record<string, any>;
}

// Mock notifications storage
export const mockNotifications: Notification[] = [
  {
    id: 'notif-1',
    userId: '2', // <PERSON><PERSON> (owner)
    type: 'approval_status',
    title: 'Hostel Registration Approved',
    message: 'Your hostel "Urban Stay Hostel" has been approved and is now live on the platform.',
    status: 'read',
    priority: 'high',
    createdAt: '2023-02-20T10:30:00Z',
    actionUrl: '/owner/hostels',
    metadata: { hostelId: '1', approvalStatus: 'approved' }
  },
  {
    id: 'notif-2',
    userId: '1', // Admin
    type: 'hostel_registration',
    title: 'New Hostel Registration',
    message: 'Green Valley Hostel has submitted a new registration request for review.',
    status: 'unread',
    priority: 'medium',
    createdAt: '2024-01-15T14:20:00Z',
    actionUrl: '/admin/hostel-approvals',
    metadata: { registrationId: 'reg-1', ownerName: 'Arjun Reddy' }
  },
  {
    id: 'notif-3',
    userId: '1', // Admin
    type: 'hostel_registration',
    title: 'New Hostel Registration',
    message: 'Tech Hub Residency has submitted a new registration request for review.',
    status: 'unread',
    priority: 'medium',
    createdAt: '2024-01-20T09:15:00Z',
    actionUrl: '/admin/hostel-approvals',
    metadata: { registrationId: 'reg-2', ownerName: 'Priya Sharma' }
  },
  {
    id: 'notif-4',
    userId: '15', // Vikram Singh (owner)
    type: 'approval_status',
    title: 'Hostel Registration Rejected',
    message: 'Your hostel registration for "Budget Stay Inn" has been rejected. Please review the feedback and resubmit.',
    status: 'unread',
    priority: 'high',
    createdAt: '2024-01-18T16:45:00Z',
    actionUrl: '/owner/hostel-registration',
    metadata: { registrationId: 'reg-3', approvalStatus: 'rejected' }
  },
  {
    id: 'notif-5',
    userId: '1', // Admin
    type: 'account_request',
    title: 'New Account Request',
    message: 'Rahul Sharma has submitted a new account request for Sharma Hospitality Services.',
    status: 'unread',
    priority: 'medium',
    createdAt: '2024-01-25T11:30:00Z',
    actionUrl: '/admin/account-approvals',
    metadata: { requestId: 'acc-req-1', applicantName: 'Rahul Sharma', businessName: 'Sharma Hospitality Services' }
  },
  {
    id: 'notif-6',
    userId: '16', // Meera Patel (approved account)
    type: 'account_request',
    title: 'Account Request Approved',
    message: 'Congratulations! Your account request for Patel Properties has been approved. You can now log in with username: meera.patel',
    status: 'read',
    priority: 'high',
    createdAt: '2024-01-22T14:20:00Z',
    actionUrl: '/owner/hostels',
    metadata: { applicantName: 'Meera Patel', businessName: 'Patel Properties', username: 'meera.patel', accountApproved: true }
  },
  {
    id: 'notif-7',
    userId: '2', // Rajesh Kumar (owner)
    type: 'system',
    title: 'Bed Allocation Completed',
    message: 'Bed A1 has been allocated to Amit Sharma',
    status: 'unread',
    priority: 'medium',
    createdAt: '2024-01-28T09:15:00Z',
    actionUrl: '/owner/bed-allocation',
    metadata: { allocationId: 'alloc-1', bedNumber: 'A1', tenantName: 'Amit Sharma' }
  },
  {
    id: 'notif-8',
    userId: 'tenant-1', // Amit Sharma (tenant)
    type: 'system',
    title: 'Bed Allocated',
    message: 'You have been allocated bed A1. Check-in date: January 15, 2024',
    status: 'read',
    priority: 'high',
    createdAt: '2024-01-15T10:30:00Z',
    actionUrl: '/tenant/my-allocation',
    metadata: { allocationId: 'alloc-1', bedNumber: 'A1', checkInDate: '2024-01-15' }
  },
  {
    id: 'notif-9',
    userId: '2', // Rajesh Kumar (owner)
    type: 'system',
    title: 'Upcoming Check-out',
    message: 'Priya Singh is scheduled to check out from bed A2 in 3 days',
    status: 'unread',
    priority: 'medium',
    createdAt: '2024-01-25T08:00:00Z',
    actionUrl: '/owner/bed-allocation?tab=upcoming',
    metadata: { allocationId: 'alloc-2', bedNumber: 'A2', tenantName: 'Priya Singh', daysRemaining: 3 }
  },
  {
    id: 'notif-10',
    userId: '3', // Priya Singh (owner)
    type: 'system',
    title: 'Maintenance Required',
    message: 'Bed B2 requires maintenance - reading light needs repair',
    status: 'unread',
    priority: 'high',
    createdAt: '2024-01-26T14:45:00Z',
    actionUrl: '/owner/bed-allocation',
    metadata: { bedId: 'bed-4', bedNumber: 'B2', maintenanceType: 'electrical', priority: 'high' }
  }
];

// Notification service functions
export class NotificationService {
  // Get notifications for a specific user
  static getNotificationsForUser(userId: string): Notification[] {
    return mockNotifications
      .filter(notification => notification.userId === userId)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }

  // Get unread notifications count for a user
  static getUnreadCount(userId: string): number {
    return mockNotifications.filter(
      notification => notification.userId === userId && notification.status === 'unread'
    ).length;
  }

  // Mark notification as read
  static markAsRead(notificationId: string): void {
    const notification = mockNotifications.find(n => n.id === notificationId);
    if (notification) {
      notification.status = 'read';
    }
  }

  // Mark all notifications as read for a user
  static markAllAsRead(userId: string): void {
    mockNotifications
      .filter(notification => notification.userId === userId)
      .forEach(notification => {
        notification.status = 'read';
      });
  }

  // Create a new notification
  static createNotification(notification: Omit<Notification, 'id' | 'createdAt'>): Notification {
    const newNotification: Notification = {
      ...notification,
      id: `notif-${Date.now()}`,
      createdAt: new Date().toISOString(),
    };
    
    mockNotifications.push(newNotification);
    return newNotification;
  }

  // Create hostel registration notification for admin
  static notifyAdminOfNewRegistration(registrationId: string, ownerName: string, hostelName: string): void {
    this.createNotification({
      userId: '1', // Admin user ID
      type: 'hostel_registration',
      title: 'New Hostel Registration',
      message: `${hostelName} has submitted a new registration request for review.`,
      status: 'unread',
      priority: 'medium',
      actionUrl: '/admin/hostel-approvals',
      metadata: { registrationId, ownerName, hostelName }
    });
  }

  // Create approval status notification for owner
  static notifyOwnerOfApprovalStatus(
    ownerId: string, 
    hostelName: string, 
    status: 'approved' | 'rejected',
    rejectionReason?: string
  ): void {
    const isApproved = status === 'approved';
    
    this.createNotification({
      userId: ownerId,
      type: 'approval_status',
      title: `Hostel Registration ${isApproved ? 'Approved' : 'Rejected'}`,
      message: isApproved 
        ? `Your hostel "${hostelName}" has been approved and is now live on the platform.`
        : `Your hostel registration for "${hostelName}" has been rejected. ${rejectionReason ? `Reason: ${rejectionReason}` : 'Please review the feedback and resubmit.'}`,
      status: 'unread',
      priority: 'high',
      actionUrl: isApproved ? '/owner/hostels' : '/owner/hostel-registration',
      metadata: { hostelName, approvalStatus: status, rejectionReason }
    });
  }

  // Create floor/room management notification
  static notifyFloorRoomUpdate(ownerId: string, action: string, details: string): void {
    this.createNotification({
      userId: ownerId,
      type: 'floor_room',
      title: 'Floor/Room Update',
      message: `${action}: ${details}`,
      status: 'unread',
      priority: 'low',
      actionUrl: '/owner/floors-rooms',
      metadata: { action, details }
    });
  }

  // Create account request notification for admin
  static notifyAdminOfNewAccountRequest(
    requestId: string,
    applicantName: string,
    businessName: string
  ): void {
    this.createNotification({
      userId: '1', // Admin user ID
      type: 'account_request',
      title: 'New Account Request',
      message: `${applicantName} has submitted a new account request for ${businessName}.`,
      status: 'unread',
      priority: 'medium',
      actionUrl: '/admin/account-approvals',
      metadata: { requestId, applicantName, businessName }
    });
  }

  // Create account approval notification for applicant
  static notifyApplicantOfAccountApproval(
    userId: string,
    applicantName: string,
    businessName: string,
    username: string
  ): void {
    this.createNotification({
      userId,
      type: 'account_request',
      title: 'Account Request Approved',
      message: `Congratulations! Your account request for ${businessName} has been approved. You can now log in with username: ${username}`,
      status: 'unread',
      priority: 'high',
      actionUrl: '/owner/hostels',
      metadata: { applicantName, businessName, username, accountApproved: true }
    });
  }

  // Create account rejection notification (for future use when user accounts exist)
  static notifyApplicantOfAccountRejection(
    applicantEmail: string,
    applicantName: string,
    businessName: string,
    rejectionReason: string
  ): void {
    // This would be used if the applicant already had a basic account
    // For now, we handle rejections via email only since they don't have accounts yet
    console.log('Account rejection notification would be sent to:', {
      email: applicantEmail,
      name: applicantName,
      business: businessName,
      reason: rejectionReason
    });
  }

  // Bed allocation notifications
  static notifyBedAllocation(
    tenantId: string,
    ownerId: string,
    bedNumber: string,
    checkInDate: string,
    allocationId: string
  ): void {
    // Notify tenant
    this.createNotification({
      userId: tenantId,
      type: 'system',
      title: 'Bed Allocated',
      message: `You have been allocated bed ${bedNumber}. Check-in date: ${new Date(checkInDate).toLocaleDateString()}`,
      status: 'unread',
      priority: 'high',
      actionUrl: '/tenant/my-allocation',
      metadata: { allocationId, bedNumber, checkInDate }
    });

    // Notify owner
    this.createNotification({
      userId: ownerId,
      type: 'system',
      title: 'Bed Allocation Completed',
      message: `Bed ${bedNumber} has been successfully allocated`,
      status: 'unread',
      priority: 'medium',
      actionUrl: '/owner/bed-allocation',
      metadata: { allocationId, bedNumber }
    });
  }

  static notifyBedCheckOut(
    tenantId: string,
    ownerId: string,
    bedNumber: string,
    refundAmount: number,
    allocationId: string
  ): void {
    // Notify tenant
    this.createNotification({
      userId: tenantId,
      type: 'system',
      title: 'Check-out Completed',
      message: `Your check-out from bed ${bedNumber} has been processed. Refund: ₹${refundAmount.toLocaleString()}`,
      status: 'unread',
      priority: 'high',
      actionUrl: '/tenant/my-history',
      metadata: { allocationId, bedNumber, refundAmount }
    });

    // Notify owner
    this.createNotification({
      userId: ownerId,
      type: 'system',
      title: 'Tenant Check-out Processed',
      message: `Check-out completed for bed ${bedNumber}`,
      status: 'unread',
      priority: 'medium',
      actionUrl: '/owner/bed-allocation',
      metadata: { allocationId, bedNumber, refundAmount }
    });
  }

  static notifyMaintenanceUpdate(
    ownerId: string,
    bedNumber: string,
    status: 'maintenance' | 'available',
    notes?: string
  ): void {
    this.createNotification({
      userId: ownerId,
      type: 'system',
      title: status === 'maintenance' ? 'Bed Marked for Maintenance' : 'Maintenance Completed',
      message: `Bed ${bedNumber} status updated to ${status}${notes ? `: ${notes}` : ''}`,
      status: 'unread',
      priority: status === 'maintenance' ? 'high' : 'medium',
      actionUrl: '/owner/bed-allocation',
      metadata: { bedNumber, maintenanceStatus: status, notes }
    });
  }

  static notifyUpcomingCheckOut(
    ownerId: string,
    tenantName: string,
    bedNumber: string,
    daysRemaining: number,
    allocationId: string
  ): void {
    this.createNotification({
      userId: ownerId,
      type: 'system',
      title: 'Upcoming Check-out',
      message: `${tenantName} is scheduled to check out from bed ${bedNumber} in ${daysRemaining} days`,
      status: 'unread',
      priority: daysRemaining <= 3 ? 'high' : 'medium',
      actionUrl: '/owner/bed-allocation?tab=upcoming',
      metadata: { allocationId, bedNumber, tenantName, daysRemaining }
    });
  }

  // Delete notification
  static deleteNotification(notificationId: string): void {
    const index = mockNotifications.findIndex(n => n.id === notificationId);
    if (index !== -1) {
      mockNotifications.splice(index, 1);
    }
  }

  // Get notifications by type
  static getNotificationsByType(userId: string, type: Notification['type']): Notification[] {
    return mockNotifications
      .filter(notification => notification.userId === userId && notification.type === type)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }

  // Get high priority notifications
  static getHighPriorityNotifications(userId: string): Notification[] {
    return mockNotifications
      .filter(notification => 
        notification.userId === userId && 
        notification.priority === 'high' && 
        notification.status === 'unread'
      )
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }
}

// Status tracking utilities
export class StatusTracker {
  // Track hostel registration status changes
  static trackRegistrationStatusChange(
    registrationId: string,
    oldStatus: string,
    newStatus: string,
    changedBy: string,
    reason?: string
  ): void {
    const statusChange = {
      id: `status-${Date.now()}`,
      entityType: 'hostel_registration',
      entityId: registrationId,
      oldStatus,
      newStatus,
      changedBy,
      changedAt: new Date().toISOString(),
      reason,
    };

    // In a real app, this would be stored in a database
    console.log('Status change tracked:', statusChange);
  }

  // Track floor/room changes
  static trackFloorRoomChange(
    entityType: 'floor' | 'room',
    entityId: string,
    action: 'created' | 'updated' | 'deleted',
    changedBy: string,
    details?: Record<string, any>
  ): void {
    const change = {
      id: `change-${Date.now()}`,
      entityType,
      entityId,
      action,
      changedBy,
      changedAt: new Date().toISOString(),
      details,
    };

    // In a real app, this would be stored in a database
    console.log('Floor/Room change tracked:', change);
  }

  // Get status history for an entity
  static getStatusHistory(entityType: string, entityId: string): any[] {
    // In a real app, this would fetch from a database
    return [];
  }
}

// Utility functions for formatting notifications
export const formatNotificationTime = (createdAt: string): string => {
  const now = new Date();
  const notificationTime = new Date(createdAt);
  const diffInMinutes = Math.floor((now.getTime() - notificationTime.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours}h ago`;
  
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) return `${diffInDays}d ago`;
  
  return notificationTime.toLocaleDateString();
};

export const getPriorityColor = (priority: Notification['priority']): string => {
  switch (priority) {
    case 'high': return 'text-red-600';
    case 'medium': return 'text-yellow-600';
    case 'low': return 'text-blue-600';
    default: return 'text-gray-600';
  }
};

export const getTypeIcon = (type: Notification['type']): string => {
  switch (type) {
    case 'hostel_registration': return '🏢';
    case 'approval_status': return '✅';
    case 'floor_room': return '🏠';
    case 'booking': return '📅';
    case 'system': return '⚙️';
    case 'account_request': return '👤';
    default: return '📢';
  }
};
