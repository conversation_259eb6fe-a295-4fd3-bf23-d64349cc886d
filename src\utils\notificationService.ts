// Notification Service for Hostel Management System

export interface Notification {
  id: string;
  userId: string;
  type: 'hostel_registration' | 'approval_status' | 'floor_room' | 'booking' | 'system';
  title: string;
  message: string;
  status: 'unread' | 'read';
  priority: 'low' | 'medium' | 'high';
  createdAt: string;
  actionUrl?: string;
  metadata?: Record<string, any>;
}

// Mock notifications storage
export const mockNotifications: Notification[] = [
  {
    id: 'notif-1',
    userId: '2', // <PERSON><PERSON> (owner)
    type: 'approval_status',
    title: 'Hostel Registration Approved',
    message: 'Your hostel "Urban Stay Hostel" has been approved and is now live on the platform.',
    status: 'read',
    priority: 'high',
    createdAt: '2023-02-20T10:30:00Z',
    actionUrl: '/owner/hostels',
    metadata: { hostelId: '1', approvalStatus: 'approved' }
  },
  {
    id: 'notif-2',
    userId: '1', // Admin
    type: 'hostel_registration',
    title: 'New Hostel Registration',
    message: 'Green Valley Hostel has submitted a new registration request for review.',
    status: 'unread',
    priority: 'medium',
    createdAt: '2024-01-15T14:20:00Z',
    actionUrl: '/admin/hostel-approvals',
    metadata: { registrationId: 'reg-1', ownerName: 'Arjun Reddy' }
  },
  {
    id: 'notif-3',
    userId: '1', // Admin
    type: 'hostel_registration',
    title: 'New Hostel Registration',
    message: 'Tech Hub Residency has submitted a new registration request for review.',
    status: 'unread',
    priority: 'medium',
    createdAt: '2024-01-20T09:15:00Z',
    actionUrl: '/admin/hostel-approvals',
    metadata: { registrationId: 'reg-2', ownerName: 'Priya Sharma' }
  },
  {
    id: 'notif-4',
    userId: '15', // Vikram Singh (owner)
    type: 'approval_status',
    title: 'Hostel Registration Rejected',
    message: 'Your hostel registration for "Budget Stay Inn" has been rejected. Please review the feedback and resubmit.',
    status: 'unread',
    priority: 'high',
    createdAt: '2024-01-18T16:45:00Z',
    actionUrl: '/owner/hostel-registration',
    metadata: { registrationId: 'reg-3', approvalStatus: 'rejected' }
  }
];

// Notification service functions
export class NotificationService {
  // Get notifications for a specific user
  static getNotificationsForUser(userId: string): Notification[] {
    return mockNotifications
      .filter(notification => notification.userId === userId)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }

  // Get unread notifications count for a user
  static getUnreadCount(userId: string): number {
    return mockNotifications.filter(
      notification => notification.userId === userId && notification.status === 'unread'
    ).length;
  }

  // Mark notification as read
  static markAsRead(notificationId: string): void {
    const notification = mockNotifications.find(n => n.id === notificationId);
    if (notification) {
      notification.status = 'read';
    }
  }

  // Mark all notifications as read for a user
  static markAllAsRead(userId: string): void {
    mockNotifications
      .filter(notification => notification.userId === userId)
      .forEach(notification => {
        notification.status = 'read';
      });
  }

  // Create a new notification
  static createNotification(notification: Omit<Notification, 'id' | 'createdAt'>): Notification {
    const newNotification: Notification = {
      ...notification,
      id: `notif-${Date.now()}`,
      createdAt: new Date().toISOString(),
    };
    
    mockNotifications.push(newNotification);
    return newNotification;
  }

  // Create hostel registration notification for admin
  static notifyAdminOfNewRegistration(registrationId: string, ownerName: string, hostelName: string): void {
    this.createNotification({
      userId: '1', // Admin user ID
      type: 'hostel_registration',
      title: 'New Hostel Registration',
      message: `${hostelName} has submitted a new registration request for review.`,
      status: 'unread',
      priority: 'medium',
      actionUrl: '/admin/hostel-approvals',
      metadata: { registrationId, ownerName, hostelName }
    });
  }

  // Create approval status notification for owner
  static notifyOwnerOfApprovalStatus(
    ownerId: string, 
    hostelName: string, 
    status: 'approved' | 'rejected',
    rejectionReason?: string
  ): void {
    const isApproved = status === 'approved';
    
    this.createNotification({
      userId: ownerId,
      type: 'approval_status',
      title: `Hostel Registration ${isApproved ? 'Approved' : 'Rejected'}`,
      message: isApproved 
        ? `Your hostel "${hostelName}" has been approved and is now live on the platform.`
        : `Your hostel registration for "${hostelName}" has been rejected. ${rejectionReason ? `Reason: ${rejectionReason}` : 'Please review the feedback and resubmit.'}`,
      status: 'unread',
      priority: 'high',
      actionUrl: isApproved ? '/owner/hostels' : '/owner/hostel-registration',
      metadata: { hostelName, approvalStatus: status, rejectionReason }
    });
  }

  // Create floor/room management notification
  static notifyFloorRoomUpdate(ownerId: string, action: string, details: string): void {
    this.createNotification({
      userId: ownerId,
      type: 'floor_room',
      title: 'Floor/Room Update',
      message: `${action}: ${details}`,
      status: 'unread',
      priority: 'low',
      actionUrl: '/owner/floors-rooms',
      metadata: { action, details }
    });
  }

  // Delete notification
  static deleteNotification(notificationId: string): void {
    const index = mockNotifications.findIndex(n => n.id === notificationId);
    if (index !== -1) {
      mockNotifications.splice(index, 1);
    }
  }

  // Get notifications by type
  static getNotificationsByType(userId: string, type: Notification['type']): Notification[] {
    return mockNotifications
      .filter(notification => notification.userId === userId && notification.type === type)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }

  // Get high priority notifications
  static getHighPriorityNotifications(userId: string): Notification[] {
    return mockNotifications
      .filter(notification => 
        notification.userId === userId && 
        notification.priority === 'high' && 
        notification.status === 'unread'
      )
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }
}

// Status tracking utilities
export class StatusTracker {
  // Track hostel registration status changes
  static trackRegistrationStatusChange(
    registrationId: string,
    oldStatus: string,
    newStatus: string,
    changedBy: string,
    reason?: string
  ): void {
    const statusChange = {
      id: `status-${Date.now()}`,
      entityType: 'hostel_registration',
      entityId: registrationId,
      oldStatus,
      newStatus,
      changedBy,
      changedAt: new Date().toISOString(),
      reason,
    };

    // In a real app, this would be stored in a database
    console.log('Status change tracked:', statusChange);
  }

  // Track floor/room changes
  static trackFloorRoomChange(
    entityType: 'floor' | 'room',
    entityId: string,
    action: 'created' | 'updated' | 'deleted',
    changedBy: string,
    details?: Record<string, any>
  ): void {
    const change = {
      id: `change-${Date.now()}`,
      entityType,
      entityId,
      action,
      changedBy,
      changedAt: new Date().toISOString(),
      details,
    };

    // In a real app, this would be stored in a database
    console.log('Floor/Room change tracked:', change);
  }

  // Get status history for an entity
  static getStatusHistory(entityType: string, entityId: string): any[] {
    // In a real app, this would fetch from a database
    return [];
  }
}

// Utility functions for formatting notifications
export const formatNotificationTime = (createdAt: string): string => {
  const now = new Date();
  const notificationTime = new Date(createdAt);
  const diffInMinutes = Math.floor((now.getTime() - notificationTime.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours}h ago`;
  
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) return `${diffInDays}d ago`;
  
  return notificationTime.toLocaleDateString();
};

export const getPriorityColor = (priority: Notification['priority']): string => {
  switch (priority) {
    case 'high': return 'text-red-600';
    case 'medium': return 'text-yellow-600';
    case 'low': return 'text-blue-600';
    default: return 'text-gray-600';
  }
};

export const getTypeIcon = (type: Notification['type']): string => {
  switch (type) {
    case 'hostel_registration': return '🏢';
    case 'approval_status': return '✅';
    case 'floor_room': return '🏠';
    case 'booking': return '📅';
    case 'system': return '⚙️';
    default: return '📢';
  }
};
