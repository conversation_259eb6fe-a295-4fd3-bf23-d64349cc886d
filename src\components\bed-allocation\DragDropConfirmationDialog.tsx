import React, { useState } from 'react';
import { 
  User, 
  Bed as BedIcon, 
  Calendar, 
  CreditCard, 
  FileText, 
  CheckCircle,
  AlertTriangle,
  Star,
  Home,
  Phone,
  Mail
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Bed, 
  HostelTenant, 
  BedAllocation,
  mockRooms,
  mockFloors,
  getTenantBedCompatibilityScore
} from '@/data/mockData';

interface DragDropConfirmationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (allocationData: Partial<BedAllocation>) => void;
  bed: Bed | null;
  tenant: HostelTenant | null;
}

export const DragDropConfirmationDialog: React.FC<DragDropConfirmationDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  bed,
  tenant
}) => {
  const [allocationData, setAllocationData] = useState({
    checkInDate: new Date().toISOString().split('T')[0],
    expectedCheckOutDate: '',
    monthlyRent: bed?.pricePerMonth || 0,
    securityDeposit: (bed?.pricePerMonth || 0) * 2,
    advancePayment: bed?.pricePerMonth || 0,
    allocationNotes: ''
  });

  const [isProcessing, setIsProcessing] = useState(false);

  // Get room and floor information
  const room = bed ? mockRooms.find(r => r.id === bed.roomId) : null;
  const floor = room ? mockFloors.find(f => f.id === room.floorId) : null;
  
  // Calculate compatibility score
  const compatibilityScore = bed && tenant ? getTenantBedCompatibilityScore(tenant, bed) : 0;

  const handleConfirm = async () => {
    if (!bed || !tenant) return;

    setIsProcessing(true);
    
    try {
      const allocation: Partial<BedAllocation> = {
        bedId: bed.id,
        roomId: bed.roomId,
        hostelId: bed.hostelId,
        tenantId: tenant.id,
        allocationDate: new Date().toISOString().split('T')[0],
        checkInDate: allocationData.checkInDate,
        expectedCheckOutDate: allocationData.expectedCheckOutDate,
        monthlyRent: allocationData.monthlyRent,
        securityDeposit: allocationData.securityDeposit,
        advancePayment: allocationData.advancePayment,
        status: 'active',
        allocationNotes: allocationData.allocationNotes
      };

      await onConfirm(allocation);
      onClose();
    } catch (error) {
      console.error('Error confirming allocation:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleClose = () => {
    if (!isProcessing) {
      onClose();
    }
  };

  const getCompatibilityColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-100 border-green-200';
    if (score >= 60) return 'text-yellow-600 bg-yellow-100 border-yellow-200';
    if (score >= 40) return 'text-orange-600 bg-orange-100 border-orange-200';
    return 'text-red-600 bg-red-100 border-red-200';
  };

  const getCompatibilityIcon = (score: number) => {
    if (score >= 80) return <CheckCircle className="h-4 w-4" />;
    if (score >= 60) return <Star className="h-4 w-4" />;
    return <AlertTriangle className="h-4 w-4" />;
  };

  if (!bed || !tenant) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Confirm Bed Allocation
          </DialogTitle>
          <DialogDescription>
            Review and finalize the allocation details before processing
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Compatibility Score */}
          <Alert className={getCompatibilityColor(compatibilityScore)}>
            <div className="flex items-center gap-2">
              {getCompatibilityIcon(compatibilityScore)}
              <AlertDescription className="font-medium">
                Compatibility Score: {compatibilityScore}% 
                {compatibilityScore >= 80 && ' - Excellent Match!'}
                {compatibilityScore >= 60 && compatibilityScore < 80 && ' - Good Match'}
                {compatibilityScore >= 40 && compatibilityScore < 60 && ' - Fair Match'}
                {compatibilityScore < 40 && ' - Poor Match'}
              </AlertDescription>
            </div>
          </Alert>

          {/* Allocation Summary */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Tenant Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Tenant Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Avatar className="h-12 w-12">
                    <AvatarFallback className="bg-primary/10 text-primary font-medium">
                      {tenant.firstName.charAt(0)}{tenant.lastName.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold">{tenant.firstName} {tenant.lastName}</h3>
                    <p className="text-sm text-muted-foreground capitalize">
                      {tenant.occupation.replace('_', ' ')}
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{tenant.phone}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span>{tenant.email}</span>
                  </div>
                </div>

                <div className="pt-2 border-t">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Budget Range:</span>
                    <span className="font-medium">
                      ₹{tenant.budgetRange.min.toLocaleString()} - ₹{tenant.budgetRange.max.toLocaleString()}
                    </span>
                  </div>
                  {tenant.preferredRoomType && (
                    <div className="flex items-center justify-between text-sm mt-1">
                      <span className="text-muted-foreground">Preferred Room:</span>
                      <span className="font-medium capitalize">
                        {tenant.preferredRoomType.replace('_', ' ')}
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Bed Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BedIcon className="h-5 w-5" />
                  Bed Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                    <BedIcon className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold">Bed {bed.bedNumber}</h3>
                    <p className="text-sm text-muted-foreground">
                      {room?.roomNumber} • {floor?.floorName}
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Bed Type:</span>
                    <span className="font-medium capitalize">{bed.bedType.replace('_', ' ')}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Bed Size:</span>
                    <span className="font-medium capitalize">{bed.bedSize}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Monthly Rent:</span>
                    <span className="font-medium">₹{bed.pricePerMonth.toLocaleString()}</span>
                  </div>
                </div>

                <div className="pt-2 border-t">
                  <div className="text-sm text-muted-foreground mb-2">Amenities:</div>
                  <div className="flex flex-wrap gap-1">
                    {bed.hasStorage && <Badge variant="secondary" className="text-xs">Storage</Badge>}
                    {bed.hasPrivacyCurtain && <Badge variant="secondary" className="text-xs">Privacy Curtain</Badge>}
                    {bed.hasReadingLight && <Badge variant="secondary" className="text-xs">Reading Light</Badge>}
                    {bed.hasPowerOutlet && <Badge variant="secondary" className="text-xs">Power Outlet</Badge>}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Allocation Details Form */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Allocation Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="checkInDate">Check-in Date</Label>
                  <Input
                    id="checkInDate"
                    type="date"
                    value={allocationData.checkInDate}
                    onChange={(e) => setAllocationData(prev => ({
                      ...prev,
                      checkInDate: e.target.value
                    }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="expectedCheckOutDate">Expected Check-out Date</Label>
                  <Input
                    id="expectedCheckOutDate"
                    type="date"
                    value={allocationData.expectedCheckOutDate}
                    onChange={(e) => setAllocationData(prev => ({
                      ...prev,
                      expectedCheckOutDate: e.target.value
                    }))}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="monthlyRent">Monthly Rent (₹)</Label>
                  <Input
                    id="monthlyRent"
                    type="number"
                    value={allocationData.monthlyRent}
                    onChange={(e) => setAllocationData(prev => ({
                      ...prev,
                      monthlyRent: parseInt(e.target.value) || 0
                    }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="securityDeposit">Security Deposit (₹)</Label>
                  <Input
                    id="securityDeposit"
                    type="number"
                    value={allocationData.securityDeposit}
                    onChange={(e) => setAllocationData(prev => ({
                      ...prev,
                      securityDeposit: parseInt(e.target.value) || 0
                    }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="advancePayment">Advance Payment (₹)</Label>
                  <Input
                    id="advancePayment"
                    type="number"
                    value={allocationData.advancePayment}
                    onChange={(e) => setAllocationData(prev => ({
                      ...prev,
                      advancePayment: parseInt(e.target.value) || 0
                    }))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="allocationNotes">Allocation Notes (Optional)</Label>
                <Textarea
                  id="allocationNotes"
                  placeholder="Add any special notes or conditions for this allocation..."
                  value={allocationData.allocationNotes}
                  onChange={(e) => setAllocationData(prev => ({
                    ...prev,
                    allocationNotes: e.target.value
                  }))}
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Payment Summary */}
          <Card className="bg-muted/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Payment Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex justify-between">
                  <span>Monthly Rent:</span>
                  <span className="font-medium">₹{allocationData.monthlyRent.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Security Deposit:</span>
                  <span className="font-medium">₹{allocationData.securityDeposit.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Advance Payment:</span>
                  <span className="font-medium">₹{allocationData.advancePayment.toLocaleString()}</span>
                </div>
                <div className="flex justify-between font-semibold text-base pt-2 border-t">
                  <span>Total Initial Payment:</span>
                  <span>₹{(allocationData.securityDeposit + allocationData.advancePayment).toLocaleString()}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <Button 
              variant="outline" 
              onClick={handleClose}
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleConfirm}
              disabled={isProcessing || !allocationData.checkInDate || !allocationData.expectedCheckOutDate}
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Processing...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Confirm Allocation
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
