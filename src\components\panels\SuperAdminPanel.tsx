import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Building2, 
  Users, 
  CreditCard, 
  AlertTriangle,
  TrendingUp,
  DollarSign,
  UserCheck,
  Activity
} from 'lucide-react';
import { mockHostels, mockUsers, mockPayments, mockComplaints } from '@/data/mockData';

export const SuperAdminPanel = () => {
  // Calculate statistics
  const totalHostels = mockHostels.length;
  const activeHostels = mockHostels.filter(h => h.status === 'active').length;
  const totalUsers = mockUsers.filter(u => u.role === 'member').length;
  const totalRevenue = mockPayments.reduce((sum, p) => sum + p.splitDetails.platform, 0);
  const openComplaints = mockComplaints.filter(c => c.status === 'open').length;

  const stats = [
    {
      title: 'Total Hostels',
      value: totalHostels,
      change: '+12%',
      icon: Building2,
      color: 'text-blue-600'
    },
    {
      title: 'Active Users',
      value: totalUsers,
      change: '+8%',
      icon: Users,
      color: 'text-green-600'
    },
    {
      title: 'Platform Revenue',
      value: `₹${(totalRevenue / 1000).toFixed(0)}K`,
      change: '+15%',
      icon: DollarSign,
      color: 'text-purple-600'
    },
    {
      title: 'Open Complaints',
      value: openComplaints,
      change: '-5%',
      icon: AlertTriangle,
      color: 'text-red-600'
    }
  ];

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Super Admin Dashboard</h1>
          <p className="text-muted-foreground">System-wide management and analytics</p>
        </div>
        <Button className="bg-gradient-primary text-primary-foreground shadow-elevation">
          <Activity className="mr-2 h-4 w-4" />
          Generate Report
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index} className="hover:shadow-elevation transition-all duration-300">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  <span className={`${stat.change.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                    {stat.change}
                  </span>
                  {' '}from last month
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Main Content */}
      <div className="grid lg:grid-cols-2 gap-6">
        {/* Recent Hostels */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Recent Hostels
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockHostels.map((hostel) => (
                <div key={hostel.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <div className="font-medium">{hostel.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {hostel.city}, {hostel.state}
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant={hostel.status === 'active' ? 'default' : 'secondary'}>
                      {hostel.status}
                    </Badge>
                    <div className="text-sm text-muted-foreground mt-1">
                      {hostel.availableBeds}/{hostel.totalBeds} beds
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* System Analytics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              System Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 border rounded-lg">
                <div>
                  <div className="font-medium">Total Revenue</div>
                  <div className="text-sm text-muted-foreground">This month</div>
                </div>
                <div className="text-right">
                  <div className="text-xl font-bold">₹{(totalRevenue / 1000).toFixed(0)}K</div>
                  <div className="text-sm text-green-600">+15% growth</div>
                </div>
              </div>
              
              <div className="flex justify-between items-center p-3 border rounded-lg">
                <div>
                  <div className="font-medium">New Registrations</div>
                  <div className="text-sm text-muted-foreground">This week</div>
                </div>
                <div className="text-right">
                  <div className="text-xl font-bold">24</div>
                  <div className="text-sm text-green-600">+8% increase</div>
                </div>
              </div>

              <div className="flex justify-between items-center p-3 border rounded-lg">
                <div>
                  <div className="font-medium">Occupancy Rate</div>
                  <div className="text-sm text-muted-foreground">System average</div>
                </div>
                <div className="text-right">
                  <div className="text-xl font-bold">74%</div>
                  <div className="text-sm text-blue-600">Optimal range</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activities */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Recent System Activities
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">New hostel registered: Elite Residency</div>
                <div className="text-sm text-muted-foreground">Bangalore, Karnataka • 2 hours ago</div>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">Payment processed: ₹72,000</div>
                <div className="text-sm text-muted-foreground">Booking ID: #2 • 4 hours ago</div>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
              <div className="flex-1">
                <div className="font-medium">Complaint resolved: AC maintenance</div>
                <div className="text-sm text-muted-foreground">Urban Stay Hostel • 6 hours ago</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};