import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";

// Context Providers
import { AuthProvider } from "@/contexts/AuthContext";

// Layout Components
import { PublicLayout } from "@/components/layouts/PublicLayout";
import { AuthenticatedLayout } from "@/components/layouts/AuthenticatedLayout";

// Common Components
import { ErrorBoundary } from "@/components/common/ErrorBoundary";

// Route Protection
import { ProtectedRoute, PublicRoute } from "@/components/auth/ProtectedRoute";

// Page Components
import { LandingPage } from "@/components/LandingPage";
import { LoginPage } from "@/components/auth/LoginPage";
import { ForgotPasswordPage } from "@/components/auth/ForgotPasswordPage";
import { AccountRequestPage } from "@/pages/public/AccountRequest";
import { Dashboard } from "@/pages/Dashboard";
import { Unauthorized } from "@/pages/Unauthorized";
import NotFound from "./pages/NotFound";

// Super Admin Pages
import { SystemOverview } from "@/pages/admin/SystemOverview";
import { ManageHostels } from "@/pages/admin/ManageHostels";
import { HostelApprovalManagement } from "@/pages/admin/HostelApprovalManagement";
import { AccountApprovalManagement } from "@/pages/admin/AccountApprovalManagement";
import { UserManagement } from "@/pages/admin/UserManagement";
import { FinancialManagement } from "@/pages/admin/FinancialManagement";
import { AnalyticsReports } from "@/pages/admin/AnalyticsReports";
import { AuditLogs } from "@/pages/admin/AuditLogs";
import { ContentManagement } from "@/pages/admin/ContentManagement";
import { SystemSettings } from "@/pages/admin/SystemSettings";
import { ComplaintsManagement } from "@/pages/admin/ComplaintsManagement";

// Owner Pages
import { MyHostels } from "@/pages/owner/MyHostels";
import { HostelRegistrationPage } from "@/pages/owner/HostelRegistration";
import { FloorRoomManagement } from "@/pages/owner/FloorRoomManagement";
import { BedAllocationDashboard } from "@/pages/owner/BedAllocationDashboard";
import { EmployeeManagement } from "@/pages/owner/EmployeeManagement";
import { BookingsOverview } from "@/pages/owner/BookingsOverview";
import { RevenueDashboard } from "@/pages/owner/RevenueDashboard";
import { PropertyAnalytics } from "@/pages/owner/PropertyAnalytics";
import { GuestManagement } from "@/pages/owner/GuestManagement";
import { MaintenanceTracking } from "@/pages/owner/MaintenanceTracking";
import { MarketingPromotions } from "@/pages/owner/MarketingPromotions";
import { StaffManagement } from "@/pages/owner/StaffManagement";
import { InventoryManagement } from "@/pages/owner/InventoryManagement";
import { ReportsInsights } from "@/pages/owner/ReportsInsights";

// Employee Pages
import { ResidentsManagement } from "@/pages/employee/ResidentsManagement";
import { RoomManagement } from "@/pages/employee/RoomManagement";
import { ComplaintsHandling } from "@/pages/employee/ComplaintsHandling";
import { WorkDashboard } from "@/pages/employee/WorkDashboard";
import { TaskManagement } from "@/pages/employee/TaskManagement";

// Member Pages
import { HostelSearch } from "@/pages/member/HostelSearch";
import { MyBookings } from "@/pages/member/MyBookings";
import { ProfileManagement } from "@/pages/member/ProfileManagement";
import { PersonalProfile } from "@/pages/member/PersonalProfile";
import { PaymentHistory } from "@/pages/member/PaymentHistory";
import { SupportCenter } from "@/pages/member/SupportCenter";
import { NotificationsAlerts } from "@/pages/member/NotificationsAlerts";
import { ReferralProgram } from "@/pages/member/ReferralProgram";
import { WishlistFavorites } from "@/pages/member/WishlistFavorites";

// Route Protection Components
import { SuperAdminRoute, OwnerRoute, EmployeeRoute, MemberRoute } from "@/components/auth/ProtectedRoute";

const queryClient = new QueryClient();

const App = () => (
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
          <Routes>
            {/* Public Routes */}
            <Route path="/" element={<PublicLayout />}>
              <Route index element={
                <PublicRoute>
                  <LandingPage onPanelChange={() => {}} />
                </PublicRoute>
              } />
              <Route path="about" element={<div>About Page</div>} />
              <Route path="contact" element={<div>Contact Page</div>} />
              <Route path="features" element={<div>Features Page</div>} />
              <Route path="privacy" element={<div>Privacy Policy</div>} />
              <Route path="terms" element={<div>Terms of Service</div>} />
            </Route>

            {/* Authentication Routes */}
            <Route path="/login" element={
              <PublicRoute>
                <LoginPage />
              </PublicRoute>
            } />
            <Route path="/forgot-password" element={
              <PublicRoute>
                <ForgotPasswordPage />
              </PublicRoute>
            } />
            <Route path="/account-request" element={
              <PublicRoute>
                <AccountRequestPage />
              </PublicRoute>
            } />

            {/* Protected Routes */}
            <Route path="/dashboard" element={
              <ProtectedRoute>
                <AuthenticatedLayout />
              </ProtectedRoute>
            }>
              <Route index element={<Dashboard />} />
            </Route>

            {/* Super Admin Routes */}
            <Route path="/admin" element={
              <SuperAdminRoute>
                <AuthenticatedLayout />
              </SuperAdminRoute>
            }>
              <Route path="overview" element={<SystemOverview />} />
              <Route path="hostels" element={<ManageHostels />} />
              <Route path="hostel-approvals" element={<HostelApprovalManagement />} />
              <Route path="account-approvals" element={<AccountApprovalManagement />} />
              <Route path="users" element={<UserManagement />} />
              <Route path="financial" element={<FinancialManagement />} />
              <Route path="analytics" element={<AnalyticsReports />} />
              <Route path="audit-logs" element={<AuditLogs />} />
              <Route path="content" element={<ContentManagement />} />
              <Route path="settings" element={<SystemSettings />} />
              <Route path="complaints" element={<ComplaintsManagement />} />
            </Route>

            {/* Owner Routes */}
            <Route path="/owner" element={
              <OwnerRoute>
                <AuthenticatedLayout />
              </OwnerRoute>
            }>
              <Route path="hostels" element={<MyHostels />} />
              <Route path="hostel-registration" element={<HostelRegistrationPage />} />
              <Route path="floors-rooms" element={<FloorRoomManagement />} />
              <Route path="bed-allocation" element={<BedAllocationDashboard />} />
              <Route path="employees" element={<EmployeeManagement />} />
              <Route path="bookings" element={<BookingsOverview />} />
              <Route path="revenue" element={<RevenueDashboard />} />
              <Route path="analytics" element={<PropertyAnalytics />} />
              <Route path="guests" element={<GuestManagement />} />
              <Route path="maintenance" element={<MaintenanceTracking />} />
              <Route path="marketing" element={<MarketingPromotions />} />
              <Route path="staff" element={<StaffManagement />} />
              <Route path="inventory" element={<InventoryManagement />} />
              <Route path="reports" element={<ReportsInsights />} />
            </Route>

            {/* Employee Routes */}
            <Route path="/employee" element={
              <EmployeeRoute>
                <AuthenticatedLayout />
              </EmployeeRoute>
            }>
              <Route path="dashboard" element={<WorkDashboard />} />
              <Route path="tasks" element={<TaskManagement />} />
              <Route path="residents" element={<ResidentsManagement />} />
              <Route path="rooms" element={<RoomManagement />} />
              <Route path="complaints" element={<ComplaintsHandling />} />
            </Route>

            {/* Member Routes */}
            <Route path="/member" element={
              <MemberRoute>
                <AuthenticatedLayout />
              </MemberRoute>
            }>
              <Route path="search" element={<HostelSearch />} />
              <Route path="bookings" element={<MyBookings />} />
              <Route path="profile" element={<ProfileManagement />} />
              <Route path="personal-profile" element={<PersonalProfile />} />
              <Route path="payment-history" element={<PaymentHistory />} />
              <Route path="support" element={<SupportCenter />} />
              <Route path="notifications" element={<NotificationsAlerts />} />
              <Route path="referrals" element={<ReferralProgram />} />
              <Route path="wishlist" element={<WishlistFavorites />} />
            </Route>

            {/* Common Protected Routes */}
            <Route path="/settings" element={
              <ProtectedRoute>
                <AuthenticatedLayout />
              </ProtectedRoute>
            }>
              <Route index element={<div>Settings Page</div>} />
            </Route>

            <Route path="/profile" element={
              <ProtectedRoute>
                <AuthenticatedLayout />
              </ProtectedRoute>
            }>
              <Route index element={<ProfileManagement />} />
            </Route>

            {/* Error Routes */}
            <Route path="/unauthorized" element={<Unauthorized />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
  </ErrorBoundary>
);

export default App;
