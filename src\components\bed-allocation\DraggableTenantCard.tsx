import React from 'react';
import { 
  User, 
  Phone, 
  Mail, 
  Briefcase, 
  GraduationCap,
  CreditCard,
  Home,
  Star,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Draggable } from './DragDropProvider';
import { HostelTenant, Bed } from '@/data/mockData';

interface DraggableTenantCardProps {
  tenant: HostelTenant;
  compatibilityBed?: Bed;
  compatibilityScore?: number;
  className?: string;
  showCompatibility?: boolean;
  disabled?: boolean;
}

export const DraggableTenantCard: React.FC<DraggableTenantCardProps> = ({
  tenant,
  compatibilityBed,
  compatibilityScore = 0,
  className = '',
  showCompatibility = false,
  disabled = false
}) => {
  const getOccupationIcon = (occupation: string) => {
    switch (occupation) {
      case 'student':
        return <GraduationCap className="h-4 w-4" />;
      case 'working_professional':
        return <Briefcase className="h-4 w-4" />;
      default:
        return <User className="h-4 w-4" />;
    }
  };

  const getCompatibilityColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-100';
    if (score >= 60) return 'text-yellow-600 bg-yellow-100';
    if (score >= 40) return 'text-orange-600 bg-orange-100';
    return 'text-red-600 bg-red-100';
  };

  const getCompatibilityIcon = (score: number) => {
    if (score >= 80) return <CheckCircle className="h-3 w-3" />;
    if (score >= 60) return <Star className="h-3 w-3" />;
    if (score >= 40) return <Clock className="h-3 w-3" />;
    return <AlertCircle className="h-3 w-3" />;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'inactive':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'blacklisted':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const isAlreadyAllocated = !!tenant.currentBedId;

  return (
    <Draggable
      item={{ type: 'tenant', data: tenant }}
      disabled={disabled || isAlreadyAllocated}
      className={`group ${className}`}
    >
      <Card className={`
        w-full border-2 relative transition-all duration-300 transform-gpu
        ${disabled || isAlreadyAllocated ? 'opacity-50 cursor-not-allowed scale-98' : 'hover:border-primary/50 cursor-grab active:cursor-grabbing hover:-translate-y-1 hover:shadow-lg'}
        ${showCompatibility && compatibilityScore > 0 ? 'ring-1 ring-blue-200' : ''}
        ${showCompatibility && compatibilityScore >= 80 ? 'ring-green-300 shadow-green-100' : ''}
        ${showCompatibility && compatibilityScore < 40 ? 'ring-red-300 shadow-red-100' : ''}
      `}>
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3">
              <Avatar className="h-10 w-10">
                <AvatarFallback className="bg-primary/10 text-primary font-medium">
                  {tenant.firstName.charAt(0)}{tenant.lastName.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-sm truncate">
                  {tenant.firstName} {tenant.lastName}
                </h3>
                <div className="flex items-center gap-1 mt-1">
                  {getOccupationIcon(tenant.occupation)}
                  <span className="text-xs text-muted-foreground capitalize">
                    {tenant.occupation.replace('_', ' ')}
                  </span>
                </div>
              </div>
            </div>
            
            {/* Status Badge */}
            <Badge 
              variant="outline" 
              className={`text-xs ${getStatusColor(tenant.status)}`}
            >
              {tenant.status}
            </Badge>
          </div>

          {/* Compatibility Score */}
          {showCompatibility && compatibilityScore > 0 && (
            <div className={`
              flex items-center gap-2 px-2 py-1 rounded-md text-xs font-medium
              ${getCompatibilityColor(compatibilityScore)}
            `}>
              {getCompatibilityIcon(compatibilityScore)}
              <span>Match: {compatibilityScore}%</span>
            </div>
          )}

          {/* Already Allocated Warning */}
          {isAlreadyAllocated && (
            <div className="flex items-center gap-2 px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800">
              <Home className="h-3 w-3" />
              <span>Already Allocated</span>
            </div>
          )}
        </CardHeader>

        <CardContent className="pt-0 space-y-3">
          {/* Contact Information */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Phone className="h-3 w-3" />
              <span className="truncate">{tenant.phone}</span>
            </div>
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Mail className="h-3 w-3" />
              <span className="truncate">{tenant.email}</span>
            </div>
          </div>

          {/* Budget Range */}
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <CreditCard className="h-3 w-3 text-muted-foreground" />
              <span className="text-xs font-medium">Budget Range</span>
            </div>
            <div className="text-xs text-muted-foreground">
              ₹{tenant.budgetRange.min.toLocaleString()} - ₹{tenant.budgetRange.max.toLocaleString()}/month
            </div>
          </div>

          {/* Room Preference */}
          {tenant.preferredRoomType && (
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Home className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs font-medium">Preferred Room</span>
              </div>
              <div className="text-xs text-muted-foreground capitalize">
                {tenant.preferredRoomType.replace('_', ' ')}
              </div>
            </div>
          )}

          {/* Professional Details */}
          {tenant.company && (
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Briefcase className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs font-medium">Company</span>
              </div>
              <div className="text-xs text-muted-foreground truncate">
                {tenant.company}
                {tenant.designation && ` • ${tenant.designation}`}
              </div>
            </div>
          )}

          {/* Monthly Income */}
          {tenant.monthlyIncome && (
            <div className="space-y-1">
              <div className="text-xs text-muted-foreground">
                Monthly Income: ₹{tenant.monthlyIncome.toLocaleString()}
              </div>
            </div>
          )}

          {/* Registration Date */}
          <div className="pt-2 border-t border-gray-100">
            <div className="text-xs text-muted-foreground">
              Registered: {new Date(tenant.registrationDate).toLocaleDateString()}
            </div>
          </div>
        </CardContent>
      </Card>
    </Draggable>
  );
};

// Tenant Cards Panel Component
interface TenantCardsPanelProps {
  tenants: HostelTenant[];
  title?: string;
  className?: string;
  showCompatibility?: boolean;
  compatibilityBeds?: Bed[];
  onTenantSelect?: (tenant: HostelTenant) => void;
}

export const TenantCardsPanel: React.FC<TenantCardsPanelProps> = ({
  tenants,
  title = "Available Tenants",
  className = '',
  showCompatibility = false,
  compatibilityBeds = [],
  onTenantSelect
}) => {
  // Filter out already allocated tenants for drag operations
  const availableTenants = tenants.filter(tenant => !tenant.currentBedId);
  const allocatedTenants = tenants.filter(tenant => tenant.currentBedId);

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">{title}</h3>
        <Badge variant="secondary" className="text-xs">
          {availableTenants.length} available
        </Badge>
      </div>

      {/* Available Tenants */}
      {availableTenants.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-muted-foreground">Ready to Allocate</h4>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {availableTenants.map((tenant) => (
              <DraggableTenantCard
                key={tenant.id}
                tenant={tenant}
                showCompatibility={showCompatibility}
                compatibilityScore={
                  showCompatibility && compatibilityBeds.length > 0
                    ? Math.max(...compatibilityBeds.map(bed => 
                        // This would use the compatibility function from DragDropProvider
                        Math.floor(Math.random() * 40) + 60 // Mock score for now
                      ))
                    : 0
                }
                className="w-full"
              />
            ))}
          </div>
        </div>
      )}

      {/* Already Allocated Tenants */}
      {allocatedTenants.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-muted-foreground">Already Allocated</h4>
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {allocatedTenants.map((tenant) => (
              <DraggableTenantCard
                key={tenant.id}
                tenant={tenant}
                disabled={true}
                className="w-full"
              />
            ))}
          </div>
        </div>
      )}

      {/* Empty State */}
      {tenants.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <User className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p className="text-sm">No tenants available</p>
        </div>
      )}
    </div>
  );
};
