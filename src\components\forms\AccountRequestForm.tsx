import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { 
  User, 
  Building2, 
  Upload,
  X,
  FileText,
  Loader2,
  Phone,
  Mail,
  MapPin,
  CreditCard,
  Briefcase
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { AccountRequest } from '@/data/mockData';

// Validation schema for account request
const accountRequestSchema = z.object({
  // Personal Information
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Valid email is required'),
  phone: z.string().regex(/^\+91\s\d{10}$/, 'Phone must be in format +91 XXXXXXXXXX'),
  dateOfBirth: z.string().min(1, 'Date of birth is required'),
  // Address
  street: z.string().min(10, 'Street address must be at least 10 characters'),
  city: z.string().min(2, 'City is required'),
  state: z.string().min(2, 'State is required'),
  pincode: z.string().regex(/^\d{6}$/, 'Pincode must be 6 digits'),
  // Business Information
  businessName: z.string().min(3, 'Business name must be at least 3 characters'),
  businessType: z.enum(['individual', 'partnership', 'company', 'llp']),
  businessLicense: z.string().min(5, 'Business license number is required'),
  gstNumber: z.string().optional(),
  panNumber: z.string().regex(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, 'Valid PAN number is required'),
  // Request Details
  reasonForRequest: z.string().min(50, 'Reason must be at least 50 characters'),
  experienceInHospitality: z.string().optional(),
  numberOfPropertiesPlanned: z.number().min(1, 'At least 1 property is required').max(50, 'Maximum 50 properties allowed'),
  estimatedInvestment: z.number().optional(),
});

type AccountRequestFormData = z.infer<typeof accountRequestSchema>;

interface AccountRequestFormProps {
  onSubmit: (data: Omit<AccountRequest, 'id' | 'status' | 'submittedDate' | 'documents'>, documents: FileList) => void;
  isLoading?: boolean;
}

const indianStates = [
  'Andhra Pradesh', 'Arunachal Pradesh', 'Assam', 'Bihar', 'Chhattisgarh', 'Goa',
  'Gujarat', 'Haryana', 'Himachal Pradesh', 'Jharkhand', 'Karnataka', 'Kerala',
  'Madhya Pradesh', 'Maharashtra', 'Manipur', 'Meghalaya', 'Mizoram', 'Nagaland',
  'Odisha', 'Punjab', 'Rajasthan', 'Sikkim', 'Tamil Nadu', 'Telangana',
  'Tripura', 'Uttar Pradesh', 'Uttarakhand', 'West Bengal'
];

const businessTypes = [
  { value: 'individual', label: 'Individual/Sole Proprietorship' },
  { value: 'partnership', label: 'Partnership' },
  { value: 'company', label: 'Private Limited Company' },
  { value: 'llp', label: 'Limited Liability Partnership (LLP)' }
];

export const AccountRequestForm: React.FC<AccountRequestFormProps> = ({
  onSubmit,
  isLoading = false
}) => {
  const [documents, setDocuments] = useState<{
    identityProof?: File;
    addressProof?: File;
    businessLicense?: File;
    panCard?: File;
    gstCertificate?: File;
  }>({});

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<AccountRequestFormData>({
    resolver: zodResolver(accountRequestSchema),
  });

  const businessType = watch('businessType');

  const handleFileUpload = (documentType: string, file: File | null) => {
    if (file) {
      setDocuments(prev => ({ ...prev, [documentType]: file }));
    } else {
      setDocuments(prev => {
        const updated = { ...prev };
        delete updated[documentType as keyof typeof updated];
        return updated;
      });
    }
  };

  const removeDocument = (documentType: string) => {
    setDocuments(prev => {
      const updated = { ...prev };
      delete updated[documentType as keyof typeof updated];
      return updated;
    });
  };

  const onFormSubmit = (data: AccountRequestFormData) => {
    // Check required documents
    const requiredDocs = ['identityProof', 'addressProof', 'businessLicense', 'panCard'];
    const missingDocs = requiredDocs.filter(doc => !documents[doc as keyof typeof documents]);
    
    if (missingDocs.length > 0) {
      alert(`Please upload the following required documents: ${missingDocs.join(', ')}`);
      return;
    }

    const formData = {
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      phone: data.phone,
      dateOfBirth: data.dateOfBirth,
      address: {
        street: data.street,
        city: data.city,
        state: data.state,
        pincode: data.pincode,
      },
      businessName: data.businessName,
      businessType: data.businessType,
      businessLicense: data.businessLicense,
      gstNumber: data.gstNumber,
      panNumber: data.panNumber,
      reasonForRequest: data.reasonForRequest,
      experienceInHospitality: data.experienceInHospitality,
      numberOfPropertiesPlanned: data.numberOfPropertiesPlanned,
      estimatedInvestment: data.estimatedInvestment,
    };

    // Create FileList-like object
    const documentFiles = Object.values(documents).filter(Boolean) as File[];
    const fileList = {
      length: documentFiles.length,
      item: (index: number) => documentFiles[index] || null,
      [Symbol.iterator]: function* () {
        for (let i = 0; i < this.length; i++) {
          yield this.item(i);
        }
      }
    } as FileList;

    onSubmit(formData, fileList);
  };

  return (
    <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-8">
      {/* Personal Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Personal Information
          </CardTitle>
          <CardDescription>
            Provide your personal details for account verification
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name *</Label>
              <Input
                id="firstName"
                placeholder="Enter your first name"
                {...register('firstName')}
                className={errors.firstName ? 'border-destructive' : ''}
              />
              {errors.firstName && (
                <p className="text-sm text-destructive">{errors.firstName.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="lastName">Last Name *</Label>
              <Input
                id="lastName"
                placeholder="Enter your last name"
                {...register('lastName')}
                className={errors.lastName ? 'border-destructive' : ''}
              />
              {errors.lastName && (
                <p className="text-sm text-destructive">{errors.lastName.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email Address *</Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter your email address"
                {...register('email')}
                className={errors.email ? 'border-destructive' : ''}
              />
              {errors.email && (
                <p className="text-sm text-destructive">{errors.email.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number *</Label>
              <Input
                id="phone"
                placeholder="+91 XXXXXXXXXX"
                {...register('phone')}
                className={errors.phone ? 'border-destructive' : ''}
              />
              {errors.phone && (
                <p className="text-sm text-destructive">{errors.phone.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="dateOfBirth">Date of Birth *</Label>
            <Input
              id="dateOfBirth"
              type="date"
              {...register('dateOfBirth')}
              className={errors.dateOfBirth ? 'border-destructive' : ''}
            />
            {errors.dateOfBirth && (
              <p className="text-sm text-destructive">{errors.dateOfBirth.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Address Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Address Information
          </CardTitle>
          <CardDescription>
            Provide your current residential/business address
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="street">Street Address *</Label>
            <Textarea
              id="street"
              placeholder="Enter complete street address"
              rows={2}
              {...register('street')}
              className={errors.street ? 'border-destructive' : ''}
            />
            {errors.street && (
              <p className="text-sm text-destructive">{errors.street.message}</p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="city">City *</Label>
              <Input
                id="city"
                placeholder="Enter city"
                {...register('city')}
                className={errors.city ? 'border-destructive' : ''}
              />
              {errors.city && (
                <p className="text-sm text-destructive">{errors.city.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="state">State *</Label>
              <Select onValueChange={(value) => setValue('state', value)}>
                <SelectTrigger className={errors.state ? 'border-destructive' : ''}>
                  <SelectValue placeholder="Select state" />
                </SelectTrigger>
                <SelectContent>
                  {indianStates.map((state) => (
                    <SelectItem key={state} value={state}>
                      {state}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.state && (
                <p className="text-sm text-destructive">{errors.state.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="pincode">Pincode *</Label>
              <Input
                id="pincode"
                placeholder="Enter 6-digit pincode"
                {...register('pincode')}
                className={errors.pincode ? 'border-destructive' : ''}
              />
              {errors.pincode && (
                <p className="text-sm text-destructive">{errors.pincode.message}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Business Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Business Information
          </CardTitle>
          <CardDescription>
            Provide details about your business or planned venture
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="businessName">Business Name *</Label>
              <Input
                id="businessName"
                placeholder="Enter your business name"
                {...register('businessName')}
                className={errors.businessName ? 'border-destructive' : ''}
              />
              {errors.businessName && (
                <p className="text-sm text-destructive">{errors.businessName.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="businessType">Business Type *</Label>
              <Select onValueChange={(value: any) => setValue('businessType', value)}>
                <SelectTrigger className={errors.businessType ? 'border-destructive' : ''}>
                  <SelectValue placeholder="Select business type" />
                </SelectTrigger>
                <SelectContent>
                  {businessTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.businessType && (
                <p className="text-sm text-destructive">{errors.businessType.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="businessLicense">Business License Number *</Label>
              <Input
                id="businessLicense"
                placeholder="Enter business license number"
                {...register('businessLicense')}
                className={errors.businessLicense ? 'border-destructive' : ''}
              />
              {errors.businessLicense && (
                <p className="text-sm text-destructive">{errors.businessLicense.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="panNumber">PAN Number *</Label>
              <Input
                id="panNumber"
                placeholder="**********"
                {...register('panNumber')}
                className={errors.panNumber ? 'border-destructive' : ''}
              />
              {errors.panNumber && (
                <p className="text-sm text-destructive">{errors.panNumber.message}</p>
              )}
            </div>
          </div>

          {(businessType === 'company' || businessType === 'partnership' || businessType === 'llp') && (
            <div className="space-y-2">
              <Label htmlFor="gstNumber">GST Number (Optional)</Label>
              <Input
                id="gstNumber"
                placeholder="Enter GST number"
                {...register('gstNumber')}
                className={errors.gstNumber ? 'border-destructive' : ''}
              />
              {errors.gstNumber && (
                <p className="text-sm text-destructive">{errors.gstNumber.message}</p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Request Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Briefcase className="h-5 w-5" />
            Request Details
          </CardTitle>
          <CardDescription>
            Tell us about your plans and experience in hospitality
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="reasonForRequest">Reason for Account Request *</Label>
            <Textarea
              id="reasonForRequest"
              placeholder="Explain why you want to join Room Buddy Hub and your business plans..."
              rows={4}
              {...register('reasonForRequest')}
              className={errors.reasonForRequest ? 'border-destructive' : ''}
            />
            {errors.reasonForRequest && (
              <p className="text-sm text-destructive">{errors.reasonForRequest.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="experienceInHospitality">Experience in Hospitality (Optional)</Label>
            <Textarea
              id="experienceInHospitality"
              placeholder="Describe your experience in hospitality or related fields..."
              rows={3}
              {...register('experienceInHospitality')}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="numberOfPropertiesPlanned">Number of Properties Planned *</Label>
              <Input
                id="numberOfPropertiesPlanned"
                type="number"
                min="1"
                max="50"
                placeholder="Enter number of properties"
                {...register('numberOfPropertiesPlanned', { valueAsNumber: true })}
                className={errors.numberOfPropertiesPlanned ? 'border-destructive' : ''}
              />
              {errors.numberOfPropertiesPlanned && (
                <p className="text-sm text-destructive">{errors.numberOfPropertiesPlanned.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="estimatedInvestment">Estimated Investment (₹) (Optional)</Label>
              <Input
                id="estimatedInvestment"
                type="number"
                min="0"
                placeholder="Enter estimated investment"
                {...register('estimatedInvestment', { valueAsNumber: true })}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Document Upload */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Required Documents
          </CardTitle>
          <CardDescription>
            Upload the following documents for verification (PDF, JPG, PNG formats accepted)
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <DocumentUpload
            label="Identity Proof (Aadhar Card/Passport/Driving License) *"
            documentType="identityProof"
            file={documents.identityProof}
            onFileChange={handleFileUpload}
            onRemove={removeDocument}
            required
          />

          <DocumentUpload
            label="Address Proof (Utility Bill/Bank Statement) *"
            documentType="addressProof"
            file={documents.addressProof}
            onFileChange={handleFileUpload}
            onRemove={removeDocument}
            required
          />

          <DocumentUpload
            label="Business License/Registration Certificate *"
            documentType="businessLicense"
            file={documents.businessLicense}
            onFileChange={handleFileUpload}
            onRemove={removeDocument}
            required
          />

          <DocumentUpload
            label="PAN Card *"
            documentType="panCard"
            file={documents.panCard}
            onFileChange={handleFileUpload}
            onRemove={removeDocument}
            required
          />

          {(businessType === 'company' || businessType === 'partnership' || businessType === 'llp') && (
            <DocumentUpload
              label="GST Certificate (Optional)"
              documentType="gstCertificate"
              file={documents.gstCertificate}
              onFileChange={handleFileUpload}
              onRemove={removeDocument}
            />
          )}
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button type="submit" disabled={isLoading} className="min-w-32">
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Submitting...
            </>
          ) : (
            'Submit Account Request'
          )}
        </Button>
      </div>
    </form>
  );
};

// Document Upload Component
interface DocumentUploadProps {
  label: string;
  documentType: string;
  file?: File;
  onFileChange: (documentType: string, file: File | null) => void;
  onRemove: (documentType: string) => void;
  required?: boolean;
}

const DocumentUpload: React.FC<DocumentUploadProps> = ({
  label,
  documentType,
  file,
  onFileChange,
  onRemove,
  required = false
}) => {
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      // Validate file type
      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
      if (!allowedTypes.includes(selectedFile.type)) {
        alert('Please upload only PDF, JPG, or PNG files');
        return;
      }

      // Validate file size (max 5MB)
      if (selectedFile.size > 5 * 1024 * 1024) {
        alert('File size must be less than 5MB');
        return;
      }

      onFileChange(documentType, selectedFile);
    }
  };

  return (
    <div className="space-y-2">
      <Label className="flex items-center gap-2">
        <FileText className="h-4 w-4" />
        {label}
      </Label>

      {!file ? (
        <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4">
          <div className="text-center">
            <Upload className="mx-auto h-8 w-8 text-muted-foreground/50 mb-2" />
            <Label htmlFor={documentType} className="cursor-pointer">
              <span className="text-sm font-medium text-primary hover:text-primary/80">
                Click to upload {required ? '(Required)' : '(Optional)'}
              </span>
              <Input
                id={documentType}
                type="file"
                accept=".pdf,.jpg,.jpeg,.png"
                onChange={handleFileSelect}
                className="hidden"
              />
            </Label>
            <p className="text-xs text-muted-foreground mt-1">
              PDF, JPG, PNG up to 5MB
            </p>
          </div>
        </div>
      ) : (
        <div className="flex items-center justify-between p-3 border rounded-lg bg-muted/50">
          <div className="flex items-center gap-2">
            <FileText className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">{file.name}</span>
            <span className="text-xs text-muted-foreground">
              ({(file.size / 1024 / 1024).toFixed(2)} MB)
            </span>
          </div>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => onRemove(documentType)}
            className="h-6 w-6 p-0 text-destructive hover:text-destructive"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      )}
    </div>
  );
};
