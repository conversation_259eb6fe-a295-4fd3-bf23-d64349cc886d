// Bed Allocation Service - API layer for bed allocation and tenant management operations

import { 
  Bed, 
  BedAllocation, 
  HostelTenant,
  mockBeds,
  mockBedAllocations,
  mockHostelTenants,
  getBedById,
  getHostelTenantById,
  getAllocationByBedId,
  getBedsByHostelId,
  getAllocationsByHostelId,
  getActiveAllocationsByHostelId
} from '@/data/mockData';
import { NotificationService } from '@/utils/notificationService';

// Base API response interface
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Bed allocation request interface
interface BedAllocationRequest {
  bedId: string;
  tenantId: string;
  checkInDate: string;
  expectedCheckOutDate: string;
  monthlyRent: number;
  securityDeposit: number;
  advancePayment: number;
  allocationNotes?: string;
}

// Check-out request interface
interface CheckOutRequest {
  actualCheckOutDate: string;
  terminationReason?: string;
  refundAmount: number;
  deductionAmount: number;
  deductionReason?: string;
  checkOutNotes?: string;
}

// Maintenance update interface
interface MaintenanceUpdateRequest {
  status: 'maintenance' | 'available';
  maintenanceNotes?: string;
  lastMaintenanceDate?: string;
  nextMaintenanceDate?: string;
  estimatedCompletionDate?: string;
}

// Bed Allocation Service
export class BedAllocationService {
  // Allocate bed to tenant
  static async allocateBed(
    allocationRequest: BedAllocationRequest,
    allocatedBy: string
  ): Promise<ApiResponse<BedAllocation>> {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      const bed = getBedById(allocationRequest.bedId);
      const tenant = getHostelTenantById(allocationRequest.tenantId);

      if (!bed) {
        return {
          success: false,
          error: 'Bed not found'
        };
      }

      if (!tenant) {
        return {
          success: false,
          error: 'Tenant not found'
        };
      }

      if (bed.status !== 'available') {
        return {
          success: false,
          error: 'Bed is not available for allocation'
        };
      }

      if (tenant.currentBedId) {
        return {
          success: false,
          error: 'Tenant is already allocated to another bed'
        };
      }

      // Create new allocation
      const newAllocation: BedAllocation = {
        id: `alloc-${Date.now()}`,
        bedId: allocationRequest.bedId,
        roomId: bed.roomId,
        hostelId: bed.hostelId,
        tenantId: allocationRequest.tenantId,
        allocationDate: new Date().toISOString().split('T')[0],
        checkInDate: allocationRequest.checkInDate,
        expectedCheckOutDate: allocationRequest.expectedCheckOutDate,
        monthlyRent: allocationRequest.monthlyRent,
        securityDeposit: allocationRequest.securityDeposit,
        advancePayment: allocationRequest.advancePayment,
        status: 'active',
        allocationNotes: allocationRequest.allocationNotes,
        allocatedBy,
        createdDate: new Date().toISOString().split('T')[0],
        updatedDate: new Date().toISOString().split('T')[0]
      };

      // Update bed status
      const bedIndex = mockBeds.findIndex(b => b.id === allocationRequest.bedId);
      if (bedIndex !== -1) {
        mockBeds[bedIndex].status = 'occupied';
        mockBeds[bedIndex].currentTenantId = allocationRequest.tenantId;
        mockBeds[bedIndex].allocationDate = newAllocation.allocationDate;
        mockBeds[bedIndex].checkInDate = allocationRequest.checkInDate;
        mockBeds[bedIndex].updatedDate = new Date().toISOString().split('T')[0];
      }

      // Update tenant's current allocation
      const tenantIndex = mockHostelTenants.findIndex(t => t.id === allocationRequest.tenantId);
      if (tenantIndex !== -1) {
        mockHostelTenants[tenantIndex].currentBedId = allocationRequest.bedId;
        mockHostelTenants[tenantIndex].currentRoomId = bed.roomId;
        mockHostelTenants[tenantIndex].currentHostelId = bed.hostelId;
        mockHostelTenants[tenantIndex].lastActiveDate = new Date().toISOString().split('T')[0];
      }

      // Add allocation to records
      mockBedAllocations.push(newAllocation);

      // Create notification for tenant
      NotificationService.createNotification({
        userId: tenant.id,
        type: 'system',
        title: 'Bed Allocated',
        message: `You have been allocated bed ${bed.bedNumber}. Check-in date: ${new Date(allocationRequest.checkInDate).toLocaleDateString()}`,
        status: 'unread',
        priority: 'high',
        actionUrl: '/tenant/my-allocation',
        metadata: {
          allocationId: newAllocation.id,
          bedNumber: bed.bedNumber,
          checkInDate: allocationRequest.checkInDate
        }
      });

      // Create notification for hostel owner
      NotificationService.createNotification({
        userId: allocatedBy,
        type: 'system',
        title: 'Bed Allocation Completed',
        message: `Bed ${bed.bedNumber} has been allocated to ${tenant.firstName} ${tenant.lastName}`,
        status: 'unread',
        priority: 'medium',
        actionUrl: '/owner/bed-allocation',
        metadata: {
          allocationId: newAllocation.id,
          bedNumber: bed.bedNumber,
          tenantName: `${tenant.firstName} ${tenant.lastName}`
        }
      });

      return {
        success: true,
        data: newAllocation,
        message: 'Bed allocated successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to allocate bed'
      };
    }
  }

  // Check out tenant from bed
  static async checkOutTenant(
    allocationId: string,
    checkOutRequest: CheckOutRequest,
    processedBy: string
  ): Promise<ApiResponse<BedAllocation>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const allocationIndex = mockBedAllocations.findIndex(a => a.id === allocationId);
      if (allocationIndex === -1) {
        return {
          success: false,
          error: 'Allocation not found'
        };
      }

      const allocation = mockBedAllocations[allocationIndex];
      if (allocation.status !== 'active') {
        return {
          success: false,
          error: 'Allocation is not active'
        };
      }

      const bed = getBedById(allocation.bedId);
      const tenant = getHostelTenantById(allocation.tenantId);

      if (!bed || !tenant) {
        return {
          success: false,
          error: 'Bed or tenant not found'
        };
      }

      // Update allocation status
      mockBedAllocations[allocationIndex].status = 'terminated';
      mockBedAllocations[allocationIndex].actualCheckOutDate = checkOutRequest.actualCheckOutDate;
      mockBedAllocations[allocationIndex].terminationReason = checkOutRequest.terminationReason;
      mockBedAllocations[allocationIndex].updatedDate = new Date().toISOString().split('T')[0];

      // Update bed status
      const bedIndex = mockBeds.findIndex(b => b.id === allocation.bedId);
      if (bedIndex !== -1) {
        mockBeds[bedIndex].status = 'available';
        mockBeds[bedIndex].currentTenantId = undefined;
        mockBeds[bedIndex].allocationDate = undefined;
        mockBeds[bedIndex].checkInDate = undefined;
        mockBeds[bedIndex].checkOutDate = checkOutRequest.actualCheckOutDate;
        mockBeds[bedIndex].updatedDate = new Date().toISOString().split('T')[0];
      }

      // Update tenant's allocation
      const tenantIndex = mockHostelTenants.findIndex(t => t.id === allocation.tenantId);
      if (tenantIndex !== -1) {
        mockHostelTenants[tenantIndex].currentBedId = undefined;
        mockHostelTenants[tenantIndex].currentRoomId = undefined;
        mockHostelTenants[tenantIndex].currentHostelId = undefined;
        mockHostelTenants[tenantIndex].lastActiveDate = new Date().toISOString().split('T')[0];
      }

      // Create notification for tenant
      NotificationService.createNotification({
        userId: tenant.id,
        type: 'system',
        title: 'Check-out Completed',
        message: `Your check-out from bed ${bed.bedNumber} has been processed. Refund amount: ₹${checkOutRequest.refundAmount.toLocaleString()}`,
        status: 'unread',
        priority: 'high',
        actionUrl: '/tenant/my-history',
        metadata: {
          allocationId: allocation.id,
          bedNumber: bed.bedNumber,
          refundAmount: checkOutRequest.refundAmount
        }
      });

      // Create notification for hostel owner
      NotificationService.createNotification({
        userId: processedBy,
        type: 'system',
        title: 'Tenant Check-out Processed',
        message: `${tenant.firstName} ${tenant.lastName} has been checked out from bed ${bed.bedNumber}`,
        status: 'unread',
        priority: 'medium',
        actionUrl: '/owner/bed-allocation',
        metadata: {
          allocationId: allocation.id,
          bedNumber: bed.bedNumber,
          tenantName: `${tenant.firstName} ${tenant.lastName}`,
          refundAmount: checkOutRequest.refundAmount
        }
      });

      return {
        success: true,
        data: mockBedAllocations[allocationIndex],
        message: 'Check-out completed successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to process check-out'
      };
    }
  }

  // Update bed maintenance status
  static async updateBedMaintenance(
    bedId: string,
    maintenanceRequest: MaintenanceUpdateRequest,
    updatedBy: string
  ): Promise<ApiResponse<Bed>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));

      const bedIndex = mockBeds.findIndex(b => b.id === bedId);
      if (bedIndex === -1) {
        return {
          success: false,
          error: 'Bed not found'
        };
      }

      const bed = mockBeds[bedIndex];

      // Update bed maintenance status
      mockBeds[bedIndex].status = maintenanceRequest.status;
      mockBeds[bedIndex].maintenanceNotes = maintenanceRequest.maintenanceNotes;
      mockBeds[bedIndex].lastMaintenanceDate = maintenanceRequest.lastMaintenanceDate;
      mockBeds[bedIndex].nextMaintenanceDate = maintenanceRequest.nextMaintenanceDate;
      mockBeds[bedIndex].updatedDate = new Date().toISOString().split('T')[0];

      return {
        success: true,
        data: mockBeds[bedIndex],
        message: `Bed maintenance status updated to ${maintenanceRequest.status}`
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to update bed maintenance status'
      };
    }
  }

  // Get bed allocation history for a hostel
  static async getBedAllocationHistory(hostelId: string): Promise<ApiResponse<BedAllocation[]>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));

      const allocations = getAllocationsByHostelId(hostelId);
      
      return {
        success: true,
        data: allocations
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to fetch allocation history'
      };
    }
  }

  // Get active allocations for a hostel
  static async getActiveAllocations(hostelId: string): Promise<ApiResponse<BedAllocation[]>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));

      const activeAllocations = getActiveAllocationsByHostelId(hostelId);
      
      return {
        success: true,
        data: activeAllocations
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to fetch active allocations'
      };
    }
  }

  // Get bed occupancy summary for a hostel
  static async getBedOccupancySummary(hostelId: string): Promise<ApiResponse<{
    totalBeds: number;
    occupiedBeds: number;
    availableBeds: number;
    maintenanceBeds: number;
    reservedBeds: number;
    occupancyRate: number;
  }>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));

      const beds = getBedsByHostelId(hostelId);
      
      const totalBeds = beds.length;
      const occupiedBeds = beds.filter(bed => bed.status === 'occupied').length;
      const availableBeds = beds.filter(bed => bed.status === 'available').length;
      const maintenanceBeds = beds.filter(bed => bed.status === 'maintenance').length;
      const reservedBeds = beds.filter(bed => bed.status === 'reserved').length;
      
      const occupancyRate = totalBeds > 0 ? (occupiedBeds / totalBeds) * 100 : 0;

      return {
        success: true,
        data: {
          totalBeds,
          occupiedBeds,
          availableBeds,
          maintenanceBeds,
          reservedBeds,
          occupancyRate
        }
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to fetch occupancy summary'
      };
    }
  }

  // Get upcoming events (check-ins/check-outs) for a hostel
  static async getUpcomingEvents(hostelId: string, days: number = 7): Promise<ApiResponse<Array<{
    type: 'check-in' | 'check-out';
    date: string;
    allocation: BedAllocation;
    tenant: HostelTenant;
    bed: Bed;
  }>>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));

      const today = new Date();
      const futureDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000);
      
      const activeAllocations = getActiveAllocationsByHostelId(hostelId);
      const events: Array<{
        type: 'check-in' | 'check-out';
        date: string;
        allocation: BedAllocation;
        tenant: HostelTenant;
        bed: Bed;
      }> = [];

      activeAllocations.forEach(allocation => {
        const tenant = getHostelTenantById(allocation.tenantId);
        const bed = getBedById(allocation.bedId);
        
        if (!tenant || !bed) return;

        // Check for upcoming check-outs
        const checkOutDate = new Date(allocation.expectedCheckOutDate);
        if (checkOutDate >= today && checkOutDate <= futureDate) {
          events.push({
            type: 'check-out',
            date: allocation.expectedCheckOutDate,
            allocation,
            tenant,
            bed
          });
        }
      });

      // Sort events by date
      events.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

      return {
        success: true,
        data: events
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to fetch upcoming events'
      };
    }
  }
}
