import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Building2, CheckCircle, AlertCircle, Clock } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { HostelRegistrationForm } from '@/components/forms/HostelRegistrationForm';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import {
  HostelRegistration,
  mockHostelRegistrations,
  getHostelRegistrationsByOwnerId
} from '@/data/mockData';
import { NotificationService } from '@/utils/notificationService';

export const HostelRegistrationPage: React.FC = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  // Get existing registrations for the current owner
  const existingRegistrations = user ? getHostelRegistrationsByOwnerId(user.id) : [];

  const handleSubmit = async (data: Omit<HostelRegistration, 'id' | 'ownerId' | 'ownerName' | 'ownerEmail' | 'ownerPhone' | 'status' | 'submittedDate'>) => {
    if (!user) return;

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // In a real app, this would be an API call
      const newRegistration: HostelRegistration = {
        ...data,
        id: `reg-${Date.now()}`,
        ownerId: user.id,
        ownerName: user.name,
        ownerEmail: user.email,
        ownerPhone: user.phone,
        status: 'pending',
        submittedDate: new Date().toISOString().split('T')[0],
      };

      // Add to mock data (in real app, this would be handled by the API)
      mockHostelRegistrations.push(newRegistration);

      // Create notification for admin about new registration
      NotificationService.notifyAdminOfNewRegistration(
        newRegistration.id,
        newRegistration.ownerName,
        newRegistration.name
      );

      toast({
        title: 'Registration Submitted Successfully!',
        description: 'Your hostel registration has been submitted for review. You will be notified once it\'s processed.',
      });

      setShowForm(false);
      
      // Refresh the page to show the new registration
      window.location.reload();

    } catch (error) {
      toast({
        title: 'Submission Failed',
        description: 'There was an error submitting your registration. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStatusIcon = (status: HostelRegistration['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'approved':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: HostelRegistration['status']) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
    }
  };

  if (showForm) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Register New Hostel</h1>
            <p className="text-muted-foreground">
              Submit your hostel details for approval by our admin team
            </p>
          </div>
          <Button variant="outline" onClick={() => setShowForm(false)}>
            Back to Overview
          </Button>
        </div>

        <HostelRegistrationForm 
          onSubmit={handleSubmit}
          isLoading={isSubmitting}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Hostel Registration</h1>
          <p className="text-muted-foreground">
            Register your hostels and track their approval status
          </p>
        </div>
        <Button onClick={() => setShowForm(true)} className="flex items-center gap-2">
          <Building2 className="h-4 w-4" />
          Register New Hostel
        </Button>
      </div>

      {/* Registration Process Info */}
      <Card>
        <CardHeader>
          <CardTitle>Registration Process</CardTitle>
          <CardDescription>
            How the hostel registration and approval process works
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center space-y-2">
              <div className="w-12 h-12 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto">
                <Building2 className="h-6 w-6" />
              </div>
              <h3 className="font-semibold">1. Submit Details</h3>
              <p className="text-sm text-muted-foreground">
                Fill out the comprehensive registration form with all required information
              </p>
            </div>
            <div className="text-center space-y-2">
              <div className="w-12 h-12 bg-yellow-100 text-yellow-600 rounded-full flex items-center justify-center mx-auto">
                <Clock className="h-6 w-6" />
              </div>
              <h3 className="font-semibold">2. Admin Review</h3>
              <p className="text-sm text-muted-foreground">
                Our admin team will review your submission within 3-5 business days
              </p>
            </div>
            <div className="text-center space-y-2">
              <div className="w-12 h-12 bg-green-100 text-green-600 rounded-full flex items-center justify-center mx-auto">
                <CheckCircle className="h-6 w-6" />
              </div>
              <h3 className="font-semibold">3. Get Approved</h3>
              <p className="text-sm text-muted-foreground">
                Once approved, you can start managing your hostel and accepting bookings
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Existing Registrations */}
      {existingRegistrations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Your Registration Requests</CardTitle>
            <CardDescription>
              Track the status of your hostel registration submissions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {existingRegistrations.map((registration) => (
                <div key={registration.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <h3 className="font-semibold text-lg">{registration.name}</h3>
                      <p className="text-sm text-muted-foreground">
                        {registration.address}, {registration.city}, {registration.state}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Submitted on: {new Date(registration.submittedDate).toLocaleDateString()}
                      </p>
                    </div>
                    <Badge 
                      variant="outline" 
                      className={`flex items-center gap-1 ${getStatusColor(registration.status)}`}
                    >
                      {getStatusIcon(registration.status)}
                      {registration.status.charAt(0).toUpperCase() + registration.status.slice(1)}
                    </Badge>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Total Beds:</span>
                      <p className="font-medium">{registration.totalBeds}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Price per Bed:</span>
                      <p className="font-medium">₹{registration.pricePerBed.toLocaleString()}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Contact:</span>
                      <p className="font-medium">{registration.contactPerson}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">License:</span>
                      <p className="font-medium">{registration.licenseNumber}</p>
                    </div>
                  </div>

                  {registration.status === 'rejected' && registration.rejectionReason && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Rejection Reason:</strong> {registration.rejectionReason}
                      </AlertDescription>
                    </Alert>
                  )}

                  {registration.reviewComments && (
                    <Alert>
                      <AlertDescription>
                        <strong>Admin Comments:</strong> {registration.reviewComments}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {existingRegistrations.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Building2 className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Hostel Registrations Yet</h3>
            <p className="text-muted-foreground mb-6">
              Start by registering your first hostel to begin accepting bookings
            </p>
            <Button onClick={() => setShowForm(true)} className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Register Your First Hostel
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
