import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { 
  Building2, 
  MapPin, 
  User, 
  Phone, 
  Mail, 
  CreditCard, 
  Upload,
  X,
  Plus,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { HostelRegistration } from '@/data/mockData';

// Validation schema for hostel registration
const hostelRegistrationSchema = z.object({
  name: z.string().min(3, 'Hostel name must be at least 3 characters'),
  address: z.string().min(10, 'Address must be at least 10 characters'),
  city: z.string().min(2, 'City is required'),
  state: z.string().min(2, 'State is required'),
  pincode: z.string().regex(/^\d{6}$/, 'Pincode must be 6 digits'),
  description: z.string().min(50, 'Description must be at least 50 characters'),
  totalBeds: z.number().min(10, 'Minimum 10 beds required').max(500, 'Maximum 500 beds allowed'),
  pricePerBed: z.number().min(1000, 'Minimum price is ₹1,000').max(50000, 'Maximum price is ₹50,000'),
  amenities: z.array(z.string()).min(1, 'At least one amenity is required'),
  contactPerson: z.string().min(2, 'Contact person name is required'),
  contactPhone: z.string().regex(/^\+91\s\d{10}$/, 'Phone must be in format +91 XXXXXXXXXX'),
  contactEmail: z.string().email('Valid email is required'),
  licenseNumber: z.string().min(5, 'License number is required'),
  gstNumber: z.string().optional(),
  bankAccountNumber: z.string().min(9, 'Valid account number is required'),
  ifscCode: z.string().regex(/^[A-Z]{4}0[A-Z0-9]{6}$/, 'Valid IFSC code is required'),
  bankName: z.string().min(2, 'Bank name is required'),
  accountHolderName: z.string().min(2, 'Account holder name is required'),
});

type HostelRegistrationFormData = z.infer<typeof hostelRegistrationSchema>;

interface HostelRegistrationFormProps {
  onSubmit: (data: Omit<HostelRegistration, 'id' | 'ownerId' | 'ownerName' | 'ownerEmail' | 'ownerPhone' | 'status' | 'submittedDate'>) => void;
  isLoading?: boolean;
  initialData?: Partial<HostelRegistrationFormData>;
}

const availableAmenities = [
  'WiFi', 'AC', 'Heating', 'Laundry', 'Mess', 'Cafeteria', 'Gym', 'Library',
  'Study Room', 'Common Room', 'TV Room', 'Gaming Zone', 'Parking', 'Security',
  'CCTV', 'Elevator', 'Generator', 'Water Purifier', 'Housekeeping', 'Medical Facility'
];

const indianStates = [
  'Andhra Pradesh', 'Arunachal Pradesh', 'Assam', 'Bihar', 'Chhattisgarh', 'Goa',
  'Gujarat', 'Haryana', 'Himachal Pradesh', 'Jharkhand', 'Karnataka', 'Kerala',
  'Madhya Pradesh', 'Maharashtra', 'Manipur', 'Meghalaya', 'Mizoram', 'Nagaland',
  'Odisha', 'Punjab', 'Rajasthan', 'Sikkim', 'Tamil Nadu', 'Telangana',
  'Tripura', 'Uttar Pradesh', 'Uttarakhand', 'West Bengal'
];

export const HostelRegistrationForm: React.FC<HostelRegistrationFormProps> = ({
  onSubmit,
  isLoading = false,
  initialData
}) => {
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>(initialData?.amenities || []);
  const [images, setImages] = useState<File[]>([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<HostelRegistrationFormData>({
    resolver: zodResolver(hostelRegistrationSchema),
    defaultValues: {
      ...initialData,
      amenities: selectedAmenities,
    },
  });

  const handleAmenityToggle = (amenity: string) => {
    const updatedAmenities = selectedAmenities.includes(amenity)
      ? selectedAmenities.filter(a => a !== amenity)
      : [...selectedAmenities, amenity];
    
    setSelectedAmenities(updatedAmenities);
    setValue('amenities', updatedAmenities);
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setImages(prev => [...prev, ...files].slice(0, 5)); // Max 5 images
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  const onFormSubmit = (data: HostelRegistrationFormData) => {
    const formData = {
      ...data,
      amenities: selectedAmenities,
      images: images.map(img => URL.createObjectURL(img)), // In real app, upload to server
      bankAccountDetails: {
        accountNumber: data.bankAccountNumber,
        ifscCode: data.ifscCode,
        bankName: data.bankName,
        accountHolderName: data.accountHolderName,
      },
    };

    // Remove individual bank fields as they're now in bankAccountDetails
    const { bankAccountNumber, ifscCode, bankName, accountHolderName, ...submitData } = formData;
    
    onSubmit(submitData);
  };

  return (
    <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-8">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Basic Information
          </CardTitle>
          <CardDescription>
            Provide basic details about your hostel property
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Hostel Name *</Label>
              <Input
                id="name"
                placeholder="Enter hostel name"
                {...register('name')}
                className={errors.name ? 'border-destructive' : ''}
              />
              {errors.name && (
                <p className="text-sm text-destructive">{errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="totalBeds">Total Beds *</Label>
              <Input
                id="totalBeds"
                type="number"
                placeholder="Enter total number of beds"
                {...register('totalBeds', { valueAsNumber: true })}
                className={errors.totalBeds ? 'border-destructive' : ''}
              />
              {errors.totalBeds && (
                <p className="text-sm text-destructive">{errors.totalBeds.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              placeholder="Describe your hostel, its features, and what makes it special..."
              rows={4}
              {...register('description')}
              className={errors.description ? 'border-destructive' : ''}
            />
            {errors.description && (
              <p className="text-sm text-destructive">{errors.description.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="pricePerBed">Price per Bed (₹/month) *</Label>
            <Input
              id="pricePerBed"
              type="number"
              placeholder="Enter monthly price per bed"
              {...register('pricePerBed', { valueAsNumber: true })}
              className={errors.pricePerBed ? 'border-destructive' : ''}
            />
            {errors.pricePerBed && (
              <p className="text-sm text-destructive">{errors.pricePerBed.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Location Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Location Details
          </CardTitle>
          <CardDescription>
            Provide the complete address of your hostel
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="address">Complete Address *</Label>
            <Textarea
              id="address"
              placeholder="Enter complete address with landmarks"
              rows={3}
              {...register('address')}
              className={errors.address ? 'border-destructive' : ''}
            />
            {errors.address && (
              <p className="text-sm text-destructive">{errors.address.message}</p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="city">City *</Label>
              <Input
                id="city"
                placeholder="Enter city"
                {...register('city')}
                className={errors.city ? 'border-destructive' : ''}
              />
              {errors.city && (
                <p className="text-sm text-destructive">{errors.city.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="state">State *</Label>
              <Select onValueChange={(value) => setValue('state', value)}>
                <SelectTrigger className={errors.state ? 'border-destructive' : ''}>
                  <SelectValue placeholder="Select state" />
                </SelectTrigger>
                <SelectContent>
                  {indianStates.map((state) => (
                    <SelectItem key={state} value={state}>
                      {state}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.state && (
                <p className="text-sm text-destructive">{errors.state.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="pincode">Pincode *</Label>
              <Input
                id="pincode"
                placeholder="Enter 6-digit pincode"
                {...register('pincode')}
                className={errors.pincode ? 'border-destructive' : ''}
              />
              {errors.pincode && (
                <p className="text-sm text-destructive">{errors.pincode.message}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Amenities */}
      <Card>
        <CardHeader>
          <CardTitle>Amenities & Facilities</CardTitle>
          <CardDescription>
            Select all amenities and facilities available at your hostel
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {availableAmenities.map((amenity) => (
              <div key={amenity} className="flex items-center space-x-2">
                <Checkbox
                  id={amenity}
                  checked={selectedAmenities.includes(amenity)}
                  onCheckedChange={() => handleAmenityToggle(amenity)}
                />
                <Label htmlFor={amenity} className="text-sm font-normal">
                  {amenity}
                </Label>
              </div>
            ))}
          </div>
          {errors.amenities && (
            <p className="text-sm text-destructive mt-2">{errors.amenities.message}</p>
          )}
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Contact Information
          </CardTitle>
          <CardDescription>
            Provide contact details for communication regarding your hostel
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="contactPerson">Contact Person *</Label>
              <Input
                id="contactPerson"
                placeholder="Enter contact person name"
                {...register('contactPerson')}
                className={errors.contactPerson ? 'border-destructive' : ''}
              />
              {errors.contactPerson && (
                <p className="text-sm text-destructive">{errors.contactPerson.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="contactPhone">Contact Phone *</Label>
              <Input
                id="contactPhone"
                placeholder="+91 XXXXXXXXXX"
                {...register('contactPhone')}
                className={errors.contactPhone ? 'border-destructive' : ''}
              />
              {errors.contactPhone && (
                <p className="text-sm text-destructive">{errors.contactPhone.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="contactEmail">Contact Email *</Label>
            <Input
              id="contactEmail"
              type="email"
              placeholder="Enter contact email"
              {...register('contactEmail')}
              className={errors.contactEmail ? 'border-destructive' : ''}
            />
            {errors.contactEmail && (
              <p className="text-sm text-destructive">{errors.contactEmail.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Legal & Banking Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Legal & Banking Information
          </CardTitle>
          <CardDescription>
            Provide legal documents and banking details for verification
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="licenseNumber">Business License Number *</Label>
              <Input
                id="licenseNumber"
                placeholder="Enter license number"
                {...register('licenseNumber')}
                className={errors.licenseNumber ? 'border-destructive' : ''}
              />
              {errors.licenseNumber && (
                <p className="text-sm text-destructive">{errors.licenseNumber.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="gstNumber">GST Number (Optional)</Label>
              <Input
                id="gstNumber"
                placeholder="Enter GST number"
                {...register('gstNumber')}
                className={errors.gstNumber ? 'border-destructive' : ''}
              />
              {errors.gstNumber && (
                <p className="text-sm text-destructive">{errors.gstNumber.message}</p>
              )}
            </div>
          </div>

          <Separator />

          <div className="space-y-4">
            <h4 className="font-medium">Bank Account Details</h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bankAccountNumber">Account Number *</Label>
                <Input
                  id="bankAccountNumber"
                  placeholder="Enter account number"
                  {...register('bankAccountNumber')}
                  className={errors.bankAccountNumber ? 'border-destructive' : ''}
                />
                {errors.bankAccountNumber && (
                  <p className="text-sm text-destructive">{errors.bankAccountNumber.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="ifscCode">IFSC Code *</Label>
                <Input
                  id="ifscCode"
                  placeholder="Enter IFSC code"
                  {...register('ifscCode')}
                  className={errors.ifscCode ? 'border-destructive' : ''}
                />
                {errors.ifscCode && (
                  <p className="text-sm text-destructive">{errors.ifscCode.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bankName">Bank Name *</Label>
                <Input
                  id="bankName"
                  placeholder="Enter bank name"
                  {...register('bankName')}
                  className={errors.bankName ? 'border-destructive' : ''}
                />
                {errors.bankName && (
                  <p className="text-sm text-destructive">{errors.bankName.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="accountHolderName">Account Holder Name *</Label>
                <Input
                  id="accountHolderName"
                  placeholder="Enter account holder name"
                  {...register('accountHolderName')}
                  className={errors.accountHolderName ? 'border-destructive' : ''}
                />
                {errors.accountHolderName && (
                  <p className="text-sm text-destructive">{errors.accountHolderName.message}</p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Images Upload */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Hostel Images
          </CardTitle>
          <CardDescription>
            Upload high-quality images of your hostel (Maximum 5 images)
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
            <Upload className="mx-auto h-12 w-12 text-muted-foreground/50 mb-4" />
            <div className="space-y-2">
              <Label htmlFor="images" className="cursor-pointer">
                <span className="text-sm font-medium text-primary hover:text-primary/80">
                  Click to upload images
                </span>
                <Input
                  id="images"
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
              </Label>
              <p className="text-xs text-muted-foreground">
                PNG, JPG, JPEG up to 5MB each
              </p>
            </div>
          </div>

          {images.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              {images.map((image, index) => (
                <div key={index} className="relative group">
                  <img
                    src={URL.createObjectURL(image)}
                    alt={`Upload ${index + 1}`}
                    className="w-full h-24 object-cover rounded-lg border"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => removeImage(index)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button type="submit" disabled={isLoading} className="min-w-32">
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Submitting...
            </>
          ) : (
            'Submit Registration'
          )}
        </Button>
      </div>
    </form>
  );
};
