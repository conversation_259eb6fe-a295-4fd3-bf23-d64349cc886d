// Email Service - Handle email notifications for account management

import { AccountRequest, User } from '@/data/mockData';

// Email template interfaces
interface EmailTemplate {
  subject: string;
  htmlContent: string;
  textContent: string;
}

interface AccountApprovalEmailData {
  applicantName: string;
  businessName: string;
  username: string;
  temporaryPassword: string;
  loginUrl: string;
}

interface AccountRejectionEmailData {
  applicantName: string;
  businessName: string;
  rejectionReason: string;
  reapplyUrl: string;
}

// Email service class
export class EmailService {
  // Mock email sending function (in real app, this would integrate with email providers like SendGrid, AWS SES, etc.)
  private static async sendEmail(
    to: string,
    subject: string,
    htmlContent: string,
    textContent: string
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // In a real application, you would integrate with an email service provider
      console.log('📧 Email sent:', {
        to,
        subject,
        htmlContent,
        textContent,
        timestamp: new Date().toISOString()
      });

      return {
        success: true,
        messageId: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to send email'
      };
    }
  }

  // Send account approval email with login credentials
  static async sendAccountApprovalEmail(
    email: string,
    data: AccountApprovalEmailData
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const template = this.generateAccountApprovalTemplate(data);
      
      const result = await this.sendEmail(
        email,
        template.subject,
        template.htmlContent,
        template.textContent
      );

      if (!result.success) {
        return {
          success: false,
          error: result.error
        };
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to send account approval email'
      };
    }
  }

  // Send account rejection email
  static async sendAccountRejectionEmail(
    email: string,
    data: AccountRejectionEmailData
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const template = this.generateAccountRejectionTemplate(data);
      
      const result = await this.sendEmail(
        email,
        template.subject,
        template.htmlContent,
        template.textContent
      );

      if (!result.success) {
        return {
          success: false,
          error: result.error
        };
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to send account rejection email'
      };
    }
  }

  // Send welcome email for new account holders
  static async sendWelcomeEmail(
    email: string,
    userName: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const template = this.generateWelcomeTemplate(userName);
      
      const result = await this.sendEmail(
        email,
        template.subject,
        template.htmlContent,
        template.textContent
      );

      if (!result.success) {
        return {
          success: false,
          error: result.error
        };
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to send welcome email'
      };
    }
  }

  // Generate account approval email template
  private static generateAccountApprovalTemplate(data: AccountApprovalEmailData): EmailTemplate {
    const subject = '🎉 Your Room Buddy Hub Account Has Been Approved!';
    
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Account Approved - Room Buddy Hub</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }
          .credentials-box { background: #e3f2fd; border: 2px solid #2196f3; border-radius: 8px; padding: 20px; margin: 20px 0; }
          .button { display: inline-block; background: #2196f3; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
          .warning { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🏢 Room Buddy Hub</h1>
          <h2>Welcome to Our Platform!</h2>
        </div>
        
        <div class="content">
          <h3>Congratulations, ${data.applicantName}!</h3>
          
          <p>We're excited to inform you that your account request for <strong>${data.businessName}</strong> has been approved! You can now start using Room Buddy Hub to manage your hostel properties and connect with potential guests.</p>
          
          <div class="credentials-box">
            <h4>🔐 Your Login Credentials</h4>
            <p><strong>Username:</strong> ${data.username}</p>
            <p><strong>Temporary Password:</strong> ${data.temporaryPassword}</p>
            <p><em>Please change your password after your first login for security.</em></p>
          </div>
          
          <div style="text-align: center;">
            <a href="${data.loginUrl}" class="button">Login to Your Account</a>
          </div>
          
          <div class="warning">
            <h4>⚠️ Important Security Notice</h4>
            <ul>
              <li>This is a temporary password. Please change it immediately after logging in.</li>
              <li>Never share your login credentials with anyone.</li>
              <li>If you didn't request this account, please contact our support team immediately.</li>
            </ul>
          </div>
          
          <h4>🚀 Next Steps</h4>
          <ol>
            <li>Log in to your account using the credentials above</li>
            <li>Change your temporary password to a secure one</li>
            <li>Complete your profile setup</li>
            <li>Register your first hostel property</li>
            <li>Start accepting bookings and growing your business!</li>
          </ol>
          
          <h4>📞 Need Help?</h4>
          <p>If you have any questions or need assistance getting started, our support team is here to help:</p>
          <ul>
            <li>Email: <EMAIL></li>
            <li>Phone: +91 1800-123-4567</li>
            <li>Help Center: https://help.roombuddyhub.com</li>
          </ul>
        </div>
        
        <div class="footer">
          <p>Thank you for choosing Room Buddy Hub!</p>
          <p>© 2024 Room Buddy Hub. All rights reserved.</p>
        </div>
      </body>
      </html>
    `;

    const textContent = `
      Room Buddy Hub - Account Approved!
      
      Congratulations, ${data.applicantName}!
      
      Your account request for ${data.businessName} has been approved!
      
      Login Credentials:
      Username: ${data.username}
      Temporary Password: ${data.temporaryPassword}
      
      Login URL: ${data.loginUrl}
      
      IMPORTANT: Please change your password after your first login for security.
      
      Next Steps:
      1. Log in to your account
      2. Change your temporary password
      3. Complete your profile setup
      4. Register your first hostel property
      5. Start accepting bookings!
      
      Need help? Contact <NAME_EMAIL> or +91 1800-123-4567
      
      Thank you for choosing Room Buddy Hub!
    `;

    return { subject, htmlContent, textContent };
  }

  // Generate account rejection email template
  private static generateAccountRejectionTemplate(data: AccountRejectionEmailData): EmailTemplate {
    const subject = 'Update on Your Room Buddy Hub Account Request';
    
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Account Request Update - Room Buddy Hub</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #f44336; color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }
          .reason-box { background: #ffebee; border: 2px solid #f44336; border-radius: 8px; padding: 20px; margin: 20px 0; }
          .button { display: inline-block; background: #2196f3; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🏢 Room Buddy Hub</h1>
          <h2>Account Request Update</h2>
        </div>
        
        <div class="content">
          <h3>Dear ${data.applicantName},</h3>
          
          <p>Thank you for your interest in joining Room Buddy Hub with <strong>${data.businessName}</strong>. After careful review of your application, we regret to inform you that we cannot approve your account request at this time.</p>
          
          <div class="reason-box">
            <h4>📋 Reason for Decision</h4>
            <p>${data.rejectionReason}</p>
          </div>
          
          <h4>🔄 What You Can Do Next</h4>
          <p>This decision is not final. You're welcome to address the concerns mentioned above and reapply. Here's how you can improve your application:</p>
          
          <ul>
            <li>Review and address the specific reasons mentioned above</li>
            <li>Ensure all required documents are complete and up-to-date</li>
            <li>Provide additional documentation if needed</li>
            <li>Consider gaining more experience in hospitality management</li>
          </ul>
          
          <div style="text-align: center;">
            <a href="${data.reapplyUrl}" class="button">Submit New Application</a>
          </div>
          
          <h4>📞 Need Clarification?</h4>
          <p>If you have questions about this decision or need guidance on improving your application, please don't hesitate to contact our support team:</p>
          <ul>
            <li>Email: <EMAIL></li>
            <li>Phone: +91 1800-123-4567</li>
          </ul>
          
          <p>We appreciate your understanding and look forward to the possibility of working with you in the future.</p>
        </div>
        
        <div class="footer">
          <p>Thank you for your interest in Room Buddy Hub!</p>
          <p>© 2024 Room Buddy Hub. All rights reserved.</p>
        </div>
      </body>
      </html>
    `;

    const textContent = `
      Room Buddy Hub - Account Request Update
      
      Dear ${data.applicantName},
      
      Thank you for your interest in joining Room Buddy Hub with ${data.businessName}.
      
      After careful review, we cannot approve your account request at this time.
      
      Reason: ${data.rejectionReason}
      
      You can address these concerns and reapply at: ${data.reapplyUrl}
      
      For questions, contact <NAME_EMAIL> or +91 1800-123-4567
      
      Thank you for your understanding.
      
      Room Buddy Hub Team
    `;

    return { subject, htmlContent, textContent };
  }

  // Generate welcome email template
  private static generateWelcomeTemplate(userName: string): EmailTemplate {
    const subject = '🎉 Welcome to Room Buddy Hub!';
    
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to Room Buddy Hub</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }
          .feature-box { background: white; border-radius: 8px; padding: 20px; margin: 15px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
          .button { display: inline-block; background: #2196f3; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🏢 Room Buddy Hub</h1>
          <h2>Welcome to Our Community!</h2>
        </div>
        
        <div class="content">
          <h3>Hello ${userName}!</h3>
          
          <p>Welcome to Room Buddy Hub! We're thrilled to have you join our community of hostel owners and hospitality professionals.</p>
          
          <div class="feature-box">
            <h4>🚀 What You Can Do Now</h4>
            <ul>
              <li>Register your hostel properties</li>
              <li>Manage rooms and pricing</li>
              <li>Accept and track bookings</li>
              <li>Analyze your business performance</li>
              <li>Connect with guests and grow your business</li>
            </ul>
          </div>
          
          <div class="feature-box">
            <h4>📚 Getting Started Resources</h4>
            <ul>
              <li>Quick Start Guide</li>
              <li>Video Tutorials</li>
              <li>Best Practices Documentation</li>
              <li>Community Forum</li>
            </ul>
          </div>
          
          <div style="text-align: center;">
            <a href="/owner/hostels" class="button">Get Started</a>
          </div>
          
          <p>If you have any questions or need assistance, our support team is always ready to help!</p>
        </div>
        
        <div class="footer">
          <p>Happy hosting!</p>
          <p>The Room Buddy Hub Team</p>
        </div>
      </body>
      </html>
    `;

    const textContent = `
      Welcome to Room Buddy Hub!
      
      Hello ${userName}!
      
      Welcome to Room Buddy Hub! We're thrilled to have you join our community.
      
      What you can do now:
      - Register your hostel properties
      - Manage rooms and pricing
      - Accept and track bookings
      - Analyze your business performance
      - Connect with guests and grow your business
      
      Need help? Contact <NAME_EMAIL>
      
      Happy hosting!
      The Room Buddy Hub Team
    `;

    return { subject, htmlContent, textContent };
  }
}
