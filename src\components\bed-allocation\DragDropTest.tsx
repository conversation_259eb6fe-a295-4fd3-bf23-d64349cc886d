import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { DragDropAllocationInterface } from './DragDropAllocationInterface';
import { 
  mockHostelTenants, 
  mockBeds, 
  getBedsByHostelId,
  getAvailableBedsByHostelId,
  HostelTenant 
} from '@/data/mockData';
import { useToast } from '@/hooks/use-toast';

export const DragDropTest: React.FC = () => {
  const { toast } = useToast();
  const hostelId = '1'; // Urban Stay Hostel

  // Get statistics
  const allBeds = getBedsByHostelId(hostelId);
  const availableBeds = getAvailableBedsByHostelId(hostelId);
  const availableTenants = mockHostelTenants.filter(tenant => !tenant.currentBedId);

  const handleAllocationRequest = async (bedId: string, tenant: HostelTenant) => {
    // Simulate allocation process
    console.log('Allocation Request:', { bedId, tenant: tenant.firstName + ' ' + tenant.lastName });
    
    // In a real app, this would call the API
    // For now, just show success message
    toast({
      title: '🎉 Allocation Successful!',
      description: `${tenant.firstName} ${tenant.lastName} has been allocated to bed ${bedId}`,
    });
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
  };

  const handleBedClick = (bed: any) => {
    console.log('Bed clicked:', bed);
    toast({
      title: 'Bed Details',
      description: `Bed ${bed.bedNumber} - ₹${bed.pricePerMonth}/month`,
    });
  };

  return (
    <div className="space-y-6">
      {/* Test Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{allBeds.length}</div>
              <div className="text-sm text-muted-foreground">Total Beds</div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{availableBeds.length}</div>
              <div className="text-sm text-muted-foreground">Available Beds</div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{availableTenants.length}</div>
              <div className="text-sm text-muted-foreground">Available Tenants</div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {Math.min(availableBeds.length, availableTenants.length)}
              </div>
              <div className="text-sm text-muted-foreground">Possible Matches</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Debug Information */}
      <Card>
        <CardHeader>
          <CardTitle>Debug Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">Available Beds:</h4>
            <div className="flex flex-wrap gap-2">
              {availableBeds.map(bed => (
                <Badge key={bed.id} variant="outline">
                  {bed.bedNumber} (₹{bed.pricePerMonth}) - {bed.status}
                </Badge>
              ))}
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-2">All Beds for Hostel {hostelId}:</h4>
            <div className="flex flex-wrap gap-2">
              {allBeds.map(bed => (
                <Badge
                  key={bed.id}
                  variant={bed.status === 'available' ? 'default' : 'secondary'}
                >
                  {bed.bedNumber} - {bed.status} {bed.currentTenantId ? `(${bed.currentTenantId})` : ''}
                </Badge>
              ))}
            </div>
          </div>
          
          <div>
            <h4 className="font-medium mb-2">Available Tenants:</h4>
            <div className="flex flex-wrap gap-2">
              {availableTenants.map(tenant => (
                <Badge key={tenant.id} variant="secondary">
                  {tenant.firstName} {tenant.lastName} (₹{tenant.budgetRange.min}-{tenant.budgetRange.max})
                </Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Drag and Drop Interface */}
      <Card>
        <CardHeader>
          <CardTitle>Drag & Drop Bed Allocation</CardTitle>
        </CardHeader>
        <CardContent>
          <DragDropAllocationInterface
            hostelId={hostelId}
            onAllocationRequest={handleAllocationRequest}
            onBedClick={handleBedClick}
          />
        </CardContent>
      </Card>
    </div>
  );
};
